#!/usr/bin/env node

/**
 * اختبار سريع للنظام الجديد
 * يقوم بفحص جميع المكونات والطرق المتاحة
 */

const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs');

const BASE_URL = 'http://localhost:3000';
const COLORS = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

function printHeader(title) {
    console.log('\n' + '='.repeat(60));
    colorLog('cyan', `🚀 ${title}`);
    console.log('='.repeat(60));
}

function printSection(title) {
    console.log('\n' + '-'.repeat(40));
    colorLog('blue', `📋 ${title}`);
    console.log('-'.repeat(40));
}

async function checkServerStatus() {
    printSection('فحص حالة الخادم');
    
    try {
        const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
        colorLog('green', '✅ الخادم يعمل بشكل طبيعي');
        colorLog('green', `📝 الرسالة: ${response.data.message}`);
        return true;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            colorLog('red', '❌ الخادم غير متاح');
            colorLog('yellow', '💡 تأكد من تشغيل الخادم: npm start');
        } else {
            colorLog('red', `❌ خطأ في الاتصال: ${error.message}`);
        }
        return false;
    }
}

async function checkCaptchaAPI() {
    printSection('فحص مفتاح AZCaptcha');
    
    try {
        const response = await axios.get(`${BASE_URL}/check-captcha-api`);
        
        if (response.data.success) {
            colorLog('green', '✅ مفتاح AZCaptcha صحيح وجاهز للاستخدام');
        } else {
            colorLog('red', '❌ مفتاح AZCaptcha غير صحيح');
            colorLog('yellow', '💡 يرجى تحديث ملف .env بمفتاح صحيح');
        }
        
        return response.data.success;
    } catch (error) {
        colorLog('red', `❌ خطأ في فحص مفتاح AZCaptcha: ${error.message}`);
        return false;
    }
}

async function testEndpoints() {
    printSection('فحص نقاط النهاية (Endpoints)');
    
    const endpoints = [
        { path: '/health', method: 'GET', name: 'فحص الحالة' },
        { path: '/check-captcha-api', method: 'GET', name: 'فحص AZCaptcha' },
        { path: '/dashboard', method: 'GET', name: 'لوحة المراقبة' },
        { path: '/api/operations', method: 'GET', name: 'API العمليات' },
        { path: '/api/stats/overall', method: 'GET', name: 'API الإحصائيات' }
    ];
    
    for (const endpoint of endpoints) {
        try {
            const response = await axios({
                method: endpoint.method,
                url: `${BASE_URL}${endpoint.path}`,
                timeout: 5000
            });
            
            colorLog('green', `✅ ${endpoint.name}: ${response.status}`);
        } catch (error) {
            if (error.response) {
                colorLog('yellow', `⚠️ ${endpoint.name}: ${error.response.status}`);
            } else {
                colorLog('red', `❌ ${endpoint.name}: ${error.message}`);
            }
        }
    }
}

async function testValidation() {
    printSection('فحص التحقق من البيانات');
    
    const testCases = [
        {
            name: 'بيانات ناقصة (API Client)',
            endpoint: '/recharge-api',
            data: { phoneNumber: '0123456789' },
            expectedStatus: 400
        },
        {
            name: 'نوع خدمة خاطئ (API Client)',
            endpoint: '/recharge-api',
            data: { 
                phoneNumber: '0123456789', 
                voucherCode: '1234567890123456',
                serviceType: 'invalid'
            },
            expectedStatus: 400
        },
        {
            name: 'بيانات ناقصة (Puppeteer)',
            endpoint: '/recharge',
            data: { phoneNumber: '0123456789' },
            expectedStatus: 400
        }
    ];
    
    for (const testCase of testCases) {
        try {
            await axios.post(`${BASE_URL}${testCase.endpoint}`, testCase.data);
            colorLog('red', `❌ ${testCase.name}: لم يحدث خطأ كما متوقع`);
        } catch (error) {
            if (error.response && error.response.status === testCase.expectedStatus) {
                colorLog('green', `✅ ${testCase.name}: تم التعامل مع الخطأ بشكل صحيح`);
            } else {
                colorLog('yellow', `⚠️ ${testCase.name}: خطأ غير متوقع`);
            }
        }
    }
}

async function checkFiles() {
    printSection('فحص الملفات المطلوبة');
    
    const requiredFiles = [
        'server.js',
        'api-client.js',
        'captcha-solver.js',
        'database.js',
        'package.json',
        'test-api-client.js',
        'example-api-client.js',
        'API_CLIENT_GUIDE.md',
        'public/index.html',
        'public/dashboard.html'
    ];
    
    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            colorLog('green', `✅ ${file}`);
        } else {
            colorLog('red', `❌ ${file} - مفقود`);
        }
    }
}

function showComparison() {
    printSection('مقارنة الطرق');
    
    console.log('┌─────────────────────┬──────────────────┬──────────────────┐');
    console.log('│ المعيار             │ Puppeteer        │ API Client       │');
    console.log('├─────────────────────┼──────────────────┼──────────────────┤');
    console.log('│ السرعة              │ 30-60 ثانية      │ 10-20 ثانية      │');
    console.log('│ استهلاك الذاكرة     │ 100-200 MB       │ 10-20 MB         │');
    console.log('│ استهلاك المعالج     │ عالي             │ منخفض            │');
    console.log('│ الاستقرار          │ متوسط            │ عالي             │');
    console.log('│ قابلية الكشف       │ متوسطة           │ منخفضة           │');
    console.log('│ سهولة الصيانة      │ معقدة            │ بسيطة            │');
    console.log('└─────────────────────┴──────────────────┴──────────────────┘');
    
    colorLog('green', '\n💡 التوصية: استخدم API Client للحصول على أفضل أداء');
}

function showUsageExamples() {
    printSection('أمثلة الاستخدام');
    
    colorLog('cyan', '🌐 الطريقة الأصلية (Puppeteer):');
    console.log('curl -X POST http://localhost:3000/recharge \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"serviceType": "4g", "phoneNumber": "0123456789", "voucherCode": "1234567890123456"}\'');
    
    colorLog('cyan', '\n⚡ الطريقة الجديدة (API Client):');
    console.log('curl -X POST http://localhost:3000/recharge-api \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"serviceType": "4g", "phoneNumber": "0123456789", "voucherCode": "1234567890123456"}\'');
}

function showNextSteps() {
    printSection('الخطوات التالية');
    
    colorLog('yellow', '📋 للبدء في الاستخدام:');
    console.log('1. تأكد من تشغيل الخادم: npm start');
    console.log('2. افتح واجهة الويب: http://localhost:3000');
    console.log('3. اختر "API Client" كطريقة التنفيذ');
    console.log('4. أدخل البيانات المطلوبة');
    
    colorLog('yellow', '\n🧪 للاختبار:');
    console.log('npm run test-api-client    # اختبار الطريقة الجديدة');
    console.log('npm run example-api-client # تشغيل الأمثلة');
    
    colorLog('yellow', '\n📊 للمراقبة:');
    console.log('http://localhost:3000/dashboard # لوحة المراقبة');
    
    colorLog('yellow', '\n📖 للمزيد من المعلومات:');
    console.log('اقرأ ملف API_CLIENT_GUIDE.md');
}

async function runQuickTest() {
    printHeader('اختبار سريع للنظام الجديد');
    
    // فحص الملفات أولاً
    checkFiles();
    
    // فحص حالة الخادم
    const serverRunning = await checkServerStatus();
    
    if (serverRunning) {
        // فحص مفتاح AZCaptcha
        await checkCaptchaAPI();
        
        // فحص نقاط النهاية
        await testEndpoints();
        
        // فحص التحقق من البيانات
        await testValidation();
    }
    
    // عرض المقارنة والأمثلة
    showComparison();
    showUsageExamples();
    showNextSteps();
    
    printHeader('انتهى الاختبار السريع');
    
    if (serverRunning) {
        colorLog('green', '🎉 النظام جاهز للاستخدام!');
    } else {
        colorLog('red', '⚠️ يرجى تشغيل الخادم أولاً: npm start');
    }
}

// تشغيل الاختبار
if (require.main === module) {
    runQuickTest().catch(error => {
        colorLog('red', `❌ خطأ في الاختبار: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { runQuickTest };
