const axios = require('axios');
const cheerio = require('cheerio');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

class AlgeriaTelecomAPIClient {
    constructor(captchaSolver) {
        this.captchaSolver = captchaSolver;
        this.session = axios.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        });
    }

    /**
     * تعبئة البطاقة باستخدام API مباشر
     * @param {string} phoneNumber - رقم الهاتف
     * @param {string} voucherCode - رقم البطاقة
     * @param {string} serviceType - نوع الخدمة (4g أو adsl)
     * @returns {Promise<Object>} - نتيجة العملية
     */
    async rechargeVoucher(phoneNumber, voucherCode, serviceType = '4g') {
        console.log(`🚀 بدء التعبئة عبر API - ${serviceType.toUpperCase()}`);
        
        try {
            // تحديد الرابط حسب نوع الخدمة
            const serviceUrls = {
                '4g': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g',
                'adsl': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
            };

            const targetUrl = serviceUrls[serviceType];
            console.log(`📡 الوصول إلى: ${targetUrl}`);

            // المرحلة الأولى: الحصول على النموذج الأول
            const stage1Result = await this.performStage1(targetUrl, phoneNumber);
            
            if (!stage1Result.success) {
                return stage1Result;
            }

            // المرحلة الثانية: تعبئة البطاقة والكابتشا الثانية
            const stage2Result = await this.performStage2(stage1Result.data, voucherCode);
            
            return stage2Result;

        } catch (error) {
            console.error('❌ خطأ في عملية التعبئة:', error.message);
            return {
                success: false,
                message: 'خطأ في النظام: ' + error.message,
                errorType: 'system_error'
            };
        }
    }

    /**
     * المرحلة الأولى: تعبئة رقم الهاتف وحل الكابتشا الأولى
     */
    async performStage1(url, phoneNumber) {
        console.log('🔸 المرحلة الأولى: تعبئة رقم الهاتف وحل الكابتشا');
        
        try {
            // الحصول على الصفحة الأولى
            const response = await this.session.get(url);
            const $ = cheerio.load(response.data);
            
            console.log('✅ تم تحميل الصفحة الأولى');

            // البحث عن النموذج وحقول الإدخال
            const form = $('form').first();
            if (form.length === 0) {
                throw new Error('لم يتم العثور على النموذج في الصفحة');
            }

            // البحث عن حقل رقم الهاتف
            const phoneField = this.findPhoneField($);
            if (!phoneField) {
                throw new Error('لم يتم العثور على حقل رقم الهاتف');
            }

            console.log(`✅ تم العثور على حقل الهاتف: ${phoneField.name}`);

            // البحث عن صورة الكابتشا
            const captchaImg = $('img[src*="captcha"], img[src*="securimage"]').first();
            if (captchaImg.length === 0) {
                throw new Error('لم يتم العثور على صورة الكابتشا');
            }

            const captchaUrl = this.resolveUrl(captchaImg.attr('src'), url);
            console.log('🔍 تم العثور على كابتشا المرحلة الأولى');

            // تنزيل وحل الكابتشا
            const captchaText = await this.solveCaptchaFromUrl(captchaUrl);
            console.log(`🧠 تم حل الكابتشا: ${captchaText}`);

            // البحث عن حقل الكابتشا
            const captchaField = this.findCaptchaField($);
            if (!captchaField) {
                throw new Error('لم يتم العثور على حقل الكابتشا');
            }

            // إعداد بيانات النموذج
            const formData = new FormData();
            
            // إضافة جميع الحقول المخفية
            $('input[type="hidden"]').each((i, el) => {
                const name = $(el).attr('name');
                const value = $(el).attr('value');
                if (name && value !== undefined) {
                    formData.append(name, value);
                }
            });

            // إضافة البيانات الأساسية
            formData.append(phoneField.name, phoneNumber);
            formData.append(captchaField.name, captchaText);

            // إرسال النموذج
            const formAction = form.attr('action') || url;
            const submitUrl = this.resolveUrl(formAction, url);
            
            console.log('📤 إرسال النموذج الأول...');
            const submitResponse = await this.session.post(submitUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Referer': url
                }
            });

            // التحقق من النتيجة
            const result = this.checkStage1Response(submitResponse.data);
            
            if (result.success) {
                console.log('✅ نجحت المرحلة الأولى');
                return {
                    success: true,
                    data: {
                        html: submitResponse.data,
                        cookies: this.session.defaults.headers.Cookie,
                        phoneNumber: phoneNumber
                    }
                };
            } else {
                return result;
            }

        } catch (error) {
            console.error('❌ خطأ في المرحلة الأولى:', error.message);
            return {
                success: false,
                message: 'خطأ في المرحلة الأولى: ' + error.message,
                errorType: 'stage1_error'
            };
        }
    }

    /**
     * المرحلة الثانية: تعبئة البطاقة وحل الكابتشا الثانية
     */
    async performStage2(stage1Data, voucherCode) {
        console.log('🔸 المرحلة الثانية: تعبئة رقم البطاقة وحل الكابتشا');
        
        try {
            const $ = cheerio.load(stage1Data.html);
            
            // البحث عن النموذج الثاني
            const form = $('form').first();
            if (form.length === 0) {
                throw new Error('لم يتم العثور على النموذج في المرحلة الثانية');
            }

            // البحث عن حقل البطاقة
            const voucherField = this.findVoucherField($);
            if (!voucherField) {
                throw new Error('لم يتم العثور على حقل البطاقة');
            }

            console.log(`✅ تم العثور على حقل البطاقة: ${voucherField.name}`);

            // البحث عن صورة الكابتشا الثانية
            const captchaImg = $('img[src*="captcha"], img[src*="securimage"]').first();
            if (captchaImg.length === 0) {
                throw new Error('لم يتم العثور على صورة الكابتشا في المرحلة الثانية');
            }

            const captchaUrl = this.resolveUrl(captchaImg.attr('src'), 'https://paiement.at.dz/');
            console.log('🔍 تم العثور على كابتشا المرحلة الثانية');

            // تنزيل وحل الكابتشا الثانية
            const captchaText = await this.solveCaptchaFromUrl(captchaUrl);
            console.log(`🧠 تم حل الكابتشا الثانية: ${captchaText}`);

            // البحث عن حقل الكابتشا
            const captchaField = this.findCaptchaField($);
            if (!captchaField) {
                throw new Error('لم يتم العثور على حقل الكابتشا في المرحلة الثانية');
            }

            // إعداد بيانات النموذج الثاني
            const formData = new FormData();
            
            // إضافة جميع الحقول المخفية
            $('input[type="hidden"]').each((i, el) => {
                const name = $(el).attr('name');
                const value = $(el).attr('value');
                if (name && value !== undefined) {
                    formData.append(name, value);
                }
            });

            // إضافة البيانات الأساسية
            formData.append(voucherField.name, voucherCode);
            formData.append(captchaField.name, captchaText);

            // إرسال النموذج الثاني
            const formAction = form.attr('action') || '';
            const submitUrl = this.resolveUrl(formAction, 'https://paiement.at.dz/');
            
            console.log('📤 إرسال النموذج الثاني...');
            const submitResponse = await this.session.post(submitUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Referer': 'https://paiement.at.dz/'
                }
            });

            // التحقق من النتيجة النهائية
            const result = this.checkFinalResponse(submitResponse.data);
            
            return result;

        } catch (error) {
            console.error('❌ خطأ في المرحلة الثانية:', error.message);
            return {
                success: false,
                message: 'خطأ في المرحلة الثانية: ' + error.message,
                errorType: 'stage2_error'
            };
        }
    }

    /**
     * البحث عن حقل رقم الهاتف
     */
    findPhoneField($) {
        const phoneSelectors = [
            'input[name*="numero"]',
            'input[name*="phone"]',
            'input[name*="tel"]',
            'input[name*="nd"]',
            'input[placeholder*="4G"]',
            'input[placeholder*="LTE"]',
            'input[placeholder*="ADSL"]',
            'input[placeholder*="N°"]'
        ];

        for (const selector of phoneSelectors) {
            const field = $(selector).first();
            if (field.length > 0) {
                return {
                    name: field.attr('name'),
                    element: field
                };
            }
        }

        // البحث عن أول حقل نص مرئي
        const textInputs = $('input[type="text"], input:not([type])');
        if (textInputs.length > 0) {
            const firstInput = textInputs.first();
            return {
                name: firstInput.attr('name'),
                element: firstInput
            };
        }

        return null;
    }

    /**
     * البحث عن حقل البطاقة
     */
    findVoucherField($) {
        const voucherSelectors = [
            'input[name="voucher"]',
            'input[name*="carte"]',
            'input[name*="voucher"]',
            'input[name*="recharge"]',
            'input[placeholder*="carte"]',
            'input[placeholder*="voucher"]',
            'input[placeholder*="recharge"]'
        ];

        for (const selector of voucherSelectors) {
            const field = $(selector).first();
            if (field.length > 0) {
                return {
                    name: field.attr('name'),
                    element: field
                };
            }
        }

        return null;
    }

    /**
     * البحث عن حقل الكابتشا
     */
    findCaptchaField($) {
        const captchaSelectors = [
            'input[name*="captcha"]',
            'input[name*="code"]',
            'input[name*="userCode"]',
            'input[name*="securimage"]',
            'input[placeholder*="code"]'
        ];

        for (const selector of captchaSelectors) {
            const field = $(selector).first();
            if (field.length > 0) {
                return {
                    name: field.attr('name'),
                    element: field
                };
            }
        }

        return null;
    }

    /**
     * تنزيل وحل الكابتشا من URL
     */
    async solveCaptchaFromUrl(captchaUrl) {
        try {
            console.log(`📥 تنزيل الكابتشا من: ${captchaUrl}`);

            const response = await this.session.get(captchaUrl, {
                responseType: 'arraybuffer'
            });

            // حفظ الصورة مؤقتاً
            const timestamp = Date.now();
            const imagePath = path.join(__dirname, `captcha_${timestamp}.png`);

            fs.writeFileSync(imagePath, response.data);
            console.log(`💾 تم حفظ الكابتشا: ${imagePath}`);

            // حل الكابتشا
            const captchaText = await this.captchaSolver.solveCaptcha(imagePath);

            // حذف الصورة المؤقتة
            fs.unlinkSync(imagePath);

            return captchaText;
        } catch (error) {
            console.error('❌ خطأ في تنزيل/حل الكابتشا:', error.message);
            throw error;
        }
    }

    /**
     * حل URL نسبي إلى مطلق
     */
    resolveUrl(relativeUrl, baseUrl) {
        if (!relativeUrl) return baseUrl;
        if (relativeUrl.startsWith('http')) return relativeUrl;
        if (relativeUrl.startsWith('/')) {
            const base = new URL(baseUrl);
            return `${base.protocol}//${base.host}${relativeUrl}`;
        }
        return new URL(relativeUrl, baseUrl).href;
    }

    /**
     * التحقق من استجابة المرحلة الأولى
     */
    checkStage1Response(html) {
        const $ = cheerio.load(html);
        const pageText = $.text().toLowerCase();

        // التحقق من أخطاء المرحلة الأولى
        const errorKeywords = [
            'code incorrect', 'captcha incorrect', 'code invalide',
            'كود خاطئ', 'كابتشا خاطئة', 'رمز خاطئ', 'غير صحيح',
            'numéro de téléphone incorrect', 'رقم الهاتف غير صحيح'
        ];

        for (const keyword of errorKeywords) {
            if (pageText.includes(keyword)) {
                return {
                    success: false,
                    message: `خطأ في المرحلة الأولى: ${keyword}`,
                    errorType: 'stage1_validation_error'
                };
            }
        }

        // التحقق من وجود حقل البطاقة (يعني نجحت المرحلة الأولى)
        const hasVoucherField = $('input[name*="voucher"], input[name*="carte"]').length > 0;

        if (hasVoucherField) {
            return { success: true };
        } else {
            return {
                success: false,
                message: 'لم يتم الانتقال للمرحلة الثانية',
                errorType: 'stage1_incomplete'
            };
        }
    }

    /**
     * التحقق من الاستجابة النهائية
     */
    checkFinalResponse(html) {
        const $ = cheerio.load(html);
        const pageText = $.text().toLowerCase();
        const fullPageContent = $.text();

        // استخراج معلومات العملية
        const extractedInfo = this.extractOperationInfo(fullPageContent);

        // التحقق من أخطاء البطاقة
        const voucherErrorKeywords = [
            'vous devez saisir un voucher',
            'voucher invalide',
            'carte invalide',
            'numéro de carte incorrect',
            'code de carte invalide',
            'carte expirée',
            'carte déjà utilisée',
            'يجب إدخال رقم البطاقة',
            'بطاقة غير صحيحة',
            'رقم البطاقة خاطئ'
        ];

        for (const keyword of voucherErrorKeywords) {
            if (pageText.includes(keyword)) {
                return {
                    success: false,
                    message: `خطأ في البطاقة: ${keyword}`,
                    errorType: 'voucher_error',
                    extractedInfo: extractedInfo,
                    pageContent: fullPageContent
                };
            }
        }

        // التحقق من أخطاء الكابتشا
        const captchaErrorKeywords = [
            'code incorrect',
            'captcha incorrect',
            'code invalide',
            'code de sécurité incorrect',
            'كود خاطئ',
            'كابتشا خاطئة',
            'رمز خاطئ'
        ];

        for (const keyword of captchaErrorKeywords) {
            if (pageText.includes(keyword)) {
                return {
                    success: false,
                    message: `خطأ في الكابتشا: ${keyword}`,
                    errorType: 'captcha_error',
                    extractedInfo: extractedInfo,
                    pageContent: fullPageContent
                };
            }
        }

        // التحقق من رسائل النجاح
        const successKeywords = [
            'recharge effectuée', 'recharge réussie', 'succès', 'success', 'réussi',
            'تمت التعبئة', 'نجحت العملية', 'تم بنجاح', 'completed', 'terminé'
        ];

        for (const keyword of successKeywords) {
            if (pageText.includes(keyword)) {
                return {
                    success: true,
                    message: 'تمت عملية التعبئة بنجاح ✅',
                    errorType: null,
                    extractedInfo: extractedInfo,
                    pageContent: fullPageContent
                };
            }
        }

        // التحقق من عدم وجود نماذج (يعني صفحة نتيجة)
        const hasForm = $('form').length > 0;
        const hasVoucherInputs = $('input[name*="carte"], input[name*="voucher"]').length > 0;
        const hasCaptcha = $('img[src*="captcha"]').length > 0;

        if (!hasForm && !hasVoucherInputs && !hasCaptcha) {
            return {
                success: true,
                message: 'تمت العملية - يرجى التحقق من النتيجة',
                errorType: null,
                extractedInfo: extractedInfo,
                pageContent: fullPageContent
            };
        }

        // افتراضياً: خطأ غير محدد
        return {
            success: false,
            message: 'العملية لم تكتمل - خطأ غير محدد',
            errorType: 'unknown_error',
            extractedInfo: extractedInfo,
            pageContent: fullPageContent
        };
    }

    /**
     * استخراج معلومات العملية من محتوى الصفحة
     */
    extractOperationInfo(content) {
        const info = {};

        // البحث عن رقم العملية
        const operationMatch = content.match(/n°\s*d[''']opération\s*[:\s]*([a-z0-9]+)/i);
        if (operationMatch) {
            info.operationNumber = operationMatch[1];
        }

        // البحث عن رقم الهاتف
        const phoneMatch = content.match(/n°\s*tél\s*[:\s]*([0-9]+)/i);
        if (phoneMatch) {
            info.phoneNumber = phoneMatch[1];
        }

        // البحث عن رقم العميل
        const clientMatch = content.match(/n°\s*client\s*[:\s]*([0-9]+)/i);
        if (clientMatch) {
            info.clientNumber = clientMatch[1];
        }

        // البحث عن التاريخ والوقت
        const dateMatch = content.match(/date\s*[:\s]*([0-9\-\/]+)/i);
        if (dateMatch) {
            info.date = dateMatch[1];
        }

        const timeMatch = content.match(/heure\s*[:\s]*([0-9:]+)/i);
        if (timeMatch) {
            info.time = timeMatch[1];
        }

        return info;
    }
}

module.exports = AlgeriaTelecomAPIClient;
