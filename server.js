const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');
const AZCaptchaSolver = require('./captcha-solver');
const Database = require('./database');
const AlgeriaTelecomAPIClient = require('./api-client');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize captcha solver, database, and API client
const captchaSolver = new AZCaptchaSolver(process.env.AZCAPTCHA_API_KEY);
const database = new Database();
const apiClient = new AlgeriaTelecomAPIClient(captchaSolver);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Route for recharging voucher (4G or ADSL)
app.post('/recharge', async (req, res) => {
    const { phoneNumber, voucherCode, serviceType = '4g' } = req.body;

    if (!phoneNumber || !voucherCode) {
        return res.status(400).json({
            success: false,
            message: 'رقم الهاتف ورقم البطاقة مطلوبان'
        });
    }

    // التحقق من نوع الخدمة
    if (!['4g', 'adsl'].includes(serviceType)) {
        return res.status(400).json({
            success: false,
            message: 'نوع الخدمة يجب أن يكون 4g أو adsl'
        });
    }

    let browser;
    let stage1Success = false;
    let stage2Success = false;
    let captchaAttempt = 1;
    let captchaAttempt2 = 1;

    try {
        console.log('بدء عملية التعبئة...');
        
        // Launch browser
        browser = await puppeteer.launch({
            headless: false, // Set to true for production
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Set user agent to avoid detection
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // تحديد الرابط حسب نوع الخدمة
        const serviceUrls = {
            '4g': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g',
            'adsl': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
        };

        const targetUrl = serviceUrls[serviceType];
        console.log(`الانتقال إلى صفحة التعبئة (${serviceType.toUpperCase()}): ${targetUrl}`);

        await page.goto(targetUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for the form to load
        await page.waitForTimeout(2000);

        // ===== المرحلة الأولى: رقم الهاتف + كابتشا =====
        console.log('🔸 المرحلة الأولى: تعبئة رقم الهاتف وحل الكابتشا');

        // Fill phone number
        console.log(`البحث عن حقل رقم الهاتف (${serviceType.toUpperCase()})...`);
        const phoneInput = await page.evaluate((serviceType) => {
            const inputs = Array.from(document.querySelectorAll('input[type="text"], input[type="tel"], input:not([type])'));

            let phoneInput = inputs.find(input =>
                input.name?.toLowerCase().includes('numero') ||
                input.name?.toLowerCase().includes('phone') ||
                input.name?.toLowerCase().includes('tel') ||
                  input.name?.toLowerCase().includes('nd') ||
                input.placeholder?.includes('4G') ||
                input.placeholder?.includes('LTE') ||
                input.placeholder?.includes('ADSL') ||
                input.placeholder?.includes('N°') ||
                input.id?.toLowerCase().includes('phone') ||
                input.id?.toLowerCase().includes('numero') ||
                input.id?.toLowerCase().includes('tel')
            );

            if (!phoneInput) {
                phoneInput = inputs.find(input =>
                    input.offsetParent !== null && input.type === 'text'
                );
            }

            return phoneInput ? inputs.indexOf(phoneInput) : -1;
        }, serviceType);

        if (phoneInput === -1) {
            throw new Error('لم يتم العثور على حقل رقم الهاتف');
        }

        const phoneInputSelector = `input:nth-of-type(${phoneInput + 1})`;
        await page.type(phoneInputSelector, phoneNumber);
        console.log(`✅ تم تعبئة رقم الهاتف: ${phoneNumber}`);

        // Solve first captcha with retry mechanism
        console.log('حل كابتشا المرحلة الأولى...');
        const captchaImageSelector = 'img[src*="captcha"], img[src*="securimage"], .captcha img';
        const captchaInputSelector = 'input[name*="captcha"], input[name*="code"], input[placeholder*="code"]';

        const maxCaptchaRetries = 3;

        while (captchaAttempt <= maxCaptchaRetries && !stage1Success) {
            try {
                console.log(`🔄 محاولة ${captchaAttempt}/${maxCaptchaRetries} لحل كابتشا المرحلة الأولى`);

                await page.waitForSelector(captchaImageSelector, { timeout: 10000 });
                console.log('✅ تم العثور على كابتشا المرحلة الأولى');

                // انتظار إضافي للتأكد من تحميل الصورة
                await page.waitForTimeout(2000);

                // التحقق من أن الصورة محملة بالكامل
                await page.waitForFunction((selector) => {
                    const img = document.querySelector(selector);
                    return img && img.complete && img.naturalHeight > 0 && img.naturalWidth > 0;
                }, { timeout: 10000 }, captchaImageSelector);

                console.log('🔄 تنزيل صورة الكابتشا...');
                const imagePath = await captchaSolver.downloadCaptchaImage(page, captchaImageSelector);

                console.log('🧠 حل الكابتشا...');
                const captchaText = await captchaSolver.solveCaptcha(imagePath);

                // البحث عن حقل إدخال الكابتشا
                await page.waitForSelector(captchaInputSelector, { timeout: 5000 });

                // مسح أي نص موجود مسبقاً
                await page.evaluate((selector) => {
                    const input = document.querySelector(selector);
                    if (input) {
                        input.value = '';
                        input.focus();
                    }
                }, captchaInputSelector);

                // تعبئة الكابتشا
                await page.type(captchaInputSelector, captchaText);
                console.log(`✅ تم تعبئة كابتشا المرحلة الأولى: ${captchaText}`);

                // انتظار قصير للتأكد من التعبئة
                await page.waitForTimeout(1000);

                await captchaSolver.cleanupImage(imagePath);

                // محاولة النقر على زر التأكيد
                console.log('النقر على زر التأكيد للمرحلة الأولى...');
                const confirmButton = await page.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]'));
                    const confirmBtn = buttons.find(btn =>
                        btn.textContent?.includes('CONFIRMER') ||
                        btn.value?.includes('CONFIRMER') ||
                        btn.textContent?.includes('Confirmer') ||
                        btn.value?.includes('Confirmer') ||
                        btn.textContent?.includes('التالي') ||
                        btn.value?.includes('التالي')
                    );

                    if (confirmBtn) {
                        confirmBtn.click();
                        return true;
                    }
                    return false;
                });

                if (!confirmButton) {
                    throw new Error('لم يتم العثور على زر التأكيد للمرحلة الأولى');
                }

                console.log('✅ تم النقر على زر التأكيد - انتظار النتيجة...');

                // انتظار للتحقق من النتيجة
                await page.waitForTimeout(2000);

                // التحقق من وجود خطأ في الكابتشا
                const errorCheck = await page.evaluate(() => {
                    const pageText = document.body.innerText.toLowerCase();
                    const errorKeywords = [
                        'code incorrect', 'captcha incorrect', 'code invalide',
                        'كود خاطئ', 'كابتشا خاطئة', 'رمز خاطئ', 'غير صحيح'
                    ];

                    for (const keyword of errorKeywords) {
                        if (pageText.includes(keyword)) {
                            return { hasError: true, errorText: keyword };
                        }
                    }

                    // التحقق من وجود نفس صورة الكابتشا (يعني لم ننتقل للصفحة التالية)
                    const captchaStillExists = document.querySelector('img[src*="captcha"], img[src*="securimage"], .captcha img') !== null;
                    const phoneInputStillExists = document.querySelector('input[name*="numero"], input[name*="phone"], input[placeholder*="4G"]') !== null;

                    if (captchaStillExists && phoneInputStillExists) {
                        return { hasError: true, errorText: 'still on same page' };
                    }

                    return { hasError: false, errorText: null };
                });

                if (errorCheck.hasError) {
                    console.log(`❌ خطأ في كابتشا المرحلة الأولى: ${errorCheck.errorText}`);
                    if (captchaAttempt < maxCaptchaRetries) {
                        console.log('🔄 إعادة المحاولة...');
                        captchaAttempt++;
                        continue;
                    } else {
                        throw new Error(`فشل في حل كابتشا المرحلة الأولى بعد ${maxCaptchaRetries} محاولات`);
                    }
                } else {
                    console.log('✅ نجح في كابتشا المرحلة الأولى - الانتقال للمرحلة الثانية');
                    stage1Success = true;
                }

            } catch (error) {
                console.log(`⚠️ خطأ في المحاولة ${captchaAttempt}: ${error.message}`);
                if (captchaAttempt < maxCaptchaRetries) {
                    console.log('🔄 إعادة المحاولة...');
                    captchaAttempt++;
                    await page.waitForTimeout(2000); // انتظار قبل إعادة المحاولة
                } else {
                    throw new Error(`فشل في حل كابتشا المرحلة الأولى بعد ${maxCaptchaRetries} محاولات: ${error.message}`);
                }
            }
        }

        // انتظار إضافي للتأكد من الانتقال للصفحة الثانية
        await page.waitForTimeout(2000);

        // Wait for new page elements to load
        try {
            await page.waitForFunction(() => {
                // Check if we're on a new page or if new elements appeared
                const inputs = document.querySelectorAll('input[type="text"], input[type="tel"], input:not([type])');
                return inputs.length > 0;
            }, { timeout: 15000 });
            console.log('✅ تم تحميل الصفحة الثانية');
        } catch (error) {
            console.log('⚠️ تحذير: قد لا تكون الصفحة الثانية محملة بالكامل');
        }

        // ===== المرحلة الثانية: رقم البطاقة + كابتشا =====
        console.log('🔸 المرحلة الثانية: تعبئة رقم البطاقة وحل الكابتشا');

        // أخذ screenshot للصفحة الثانية للتشخيص
        try {
            const timestamp = Date.now();
            await page.screenshot({
                path: `./page2_debug_${timestamp}.png`,
                fullPage: true
            });
            console.log(`📸 تم حفظ screenshot للصفحة الثانية: page2_debug_${timestamp}.png`);
        } catch (error) {
            console.log('⚠️ فشل في أخذ screenshot:', error.message);
        }

        // Fill voucher code
        console.log(`البحث عن حقل رقم البطاقة (${serviceType.toUpperCase()})...`);
        const voucherInput = await page.evaluate(() => {
            const inputs = Array.from(document.querySelectorAll('input[type="text"], input[type="tel"], input:not([type])'));

            let voucherInput;

            // البحث عن حقل البطاقة بالاسم المحدد أولاً (الأكثر دقة)
            voucherInput = inputs.find(input => input.name === 'voucher');

            if (voucherInput) {
                console.log('✅ وجد حقل البطاقة بالاسم المحدد: name="voucher"');
                return inputs.indexOf(voucherInput);
            }

            // إذا لم نجد بالاسم المحدد، ابحث بالأسماء البديلة
            voucherInput = inputs.find(input =>
                input.name?.toLowerCase().includes('carte') ||
                input.name?.toLowerCase().includes('voucher') ||
                input.name?.toLowerCase().includes('recharge') ||
                input.placeholder?.toLowerCase().includes('carte') ||
                input.placeholder?.toLowerCase().includes('voucher') ||
                input.placeholder?.toLowerCase().includes('recharge') ||
                input.id?.toLowerCase().includes('voucher') ||
                input.id?.toLowerCase().includes('carte') ||
                input.id?.toLowerCase().includes('recharge')
            );

            if (voucherInput) {
                console.log(`✅ وجد حقل البطاقة بالاسم البديل: name="${voucherInput.name}", id="${voucherInput.id}", placeholder="${voucherInput.placeholder}"`);
                return inputs.indexOf(voucherInput);
            }

            // البحث عن حقل طويل فارغ (عادة حقل البطاقة يكون أطول من حقل الكابتشا)
            voucherInput = inputs.find(input =>
                input.offsetParent !== null &&
                input.type === 'text' &&
                input.value === '' &&
                (input.maxLength >= 16 || !input.maxLength) && // حقل البطاقة عادة 16 رقم أو أكثر
                input.name !== 'userCode' // تأكد من أنه ليس حقل الكابتشا
            );

            if (voucherInput) {
                console.log(`✅ وجد حقل البطاقة بالطول: maxLength="${voucherInput.maxLength}"`);
                return inputs.indexOf(voucherInput);
            }

            console.log('❌ لم يتم العثور على حقل البطاقة');
            return -1;
        });

        if (voucherInput === -1) {
            throw new Error('لم يتم العثور على حقل رقم البطاقة');
        }

        const voucherInputSelector = `input:nth-of-type(${voucherInput + 1})`;
        await page.type(voucherInputSelector, voucherCode);
        console.log(`✅ تم تعبئة رقم البطاقة: ${voucherCode}`);

        // Solve second captcha with retry mechanism
        console.log('حل كابتشا المرحلة الثانية...');



        while (captchaAttempt2 <= maxCaptchaRetries && !stage2Success) {
            try {
                console.log(`🔄 محاولة ${captchaAttempt2}/${maxCaptchaRetries} لحل كابتشا المرحلة الثانية`);

                // انتظار تحميل الصفحة الثانية بالكامل
                await page.waitForTimeout(3000);

                // البحث عن كابتشا في الصفحة الثانية
                await page.waitForSelector(captchaImageSelector, { timeout: 10000 });
                console.log('✅ تم العثور على كابتشا المرحلة الثانية');

                // التأكد من تحميل الصورة الجديدة
                await page.waitForFunction((selector) => {
                    const img = document.querySelector(selector);
                    return img && img.complete && img.naturalHeight > 0 && img.naturalWidth > 0;
                }, { timeout: 10000 }, captchaImageSelector);

                // انتظار إضافي للتأكد من أن الصورة مختلفة عن الأولى
                await page.waitForTimeout(2000);

                console.log('🔄 تنزيل صورة كابتشا المرحلة الثانية...');
                const imagePath = await captchaSolver.downloadCaptchaImage(page, captchaImageSelector);

                console.log('🧠 حل كابتشا المرحلة الثانية...');
                const captchaText = await captchaSolver.solveCaptcha(imagePath);

            // البحث عن حقل إدخال الكابتشا في الصفحة الثانية
            console.log('🔍 البحث عن حقل الكابتشا في الصفحة الثانية...');

            const captchaInputInfo = await page.evaluate(() => {
                const inputs = Array.from(document.querySelectorAll('input'));

                // طباعة معلومات جميع الحقول للتشخيص
                console.log('🔍 جميع حقول الإدخال في الصفحة:');
                inputs.forEach((input, index) => {
                    console.log(`${index}: type="${input.type}", name="${input.name}", id="${input.id}", placeholder="${input.placeholder}", value="${input.value}", maxLength="${input.maxLength}"`);
                });

                // البحث عن حقل الكابتشا بطرق مختلفة
                let captchaInput = null;
                let captchaIndex = -1;

                // الطريقة 1: البحث بالاسم المحدد أولاً (الأكثر دقة)
                captchaInput = inputs.find(input =>
                    input.name === 'userCode' // الاسم الصحيح لحقل الكابتشا في موقع الجزائر تيليكوم
                );

                // إذا لم نجد بالاسم المحدد، ابحث بالأسماء البديلة
                if (!captchaInput) {
                    captchaInput = inputs.find(input =>
                        input.name?.toLowerCase().includes('usercode') ||
                        input.name?.toLowerCase().includes('captcha') ||
                        input.name?.toLowerCase().includes('code') ||
                        input.name?.toLowerCase().includes('securimage') ||
                        input.id?.toLowerCase().includes('captcha') ||
                        input.id?.toLowerCase().includes('code') ||
                        input.id?.toLowerCase().includes('usercode') ||
                        input.id?.toLowerCase().includes('securimage')
                    );
                }

                if (captchaInput) {
                    captchaIndex = inputs.indexOf(captchaInput);
                    console.log(`✅ وجد حقل الكابتشا بالاسم/المعرف: index=${captchaIndex}, name="${captchaInput.name}", id="${captchaInput.id}"`);
                    return { index: captchaIndex, method: 'name_id', element: captchaInput };
                }

                // الطريقة 2: البحث عن حقل قصير فارغ (عادة الكابتشا)
                captchaInput = inputs.find(input =>
                    input.type === 'text' &&
                    input.value === '' &&
                    input.maxLength &&
                    input.maxLength <= 10 &&
                    input.offsetParent !== null && // visible
                    !input.name?.toLowerCase().includes('numero') &&
                    !input.name?.toLowerCase().includes('phone') &&
                    !input.name?.toLowerCase().includes('carte') &&
                    !input.name?.toLowerCase().includes('voucher') &&
                    // لا نستبعد userCode لأنه اسم حقل الكابتشا الصحيح
                    input.name !== 'voucher' // تأكد من أنه ليس حقل البطاقة
                );

                if (captchaInput) {
                    captchaIndex = inputs.indexOf(captchaInput);
                    console.log(`✅ وجد حقل الكابتشا بالطول: index=${captchaIndex}, maxLength="${captchaInput.maxLength}"`);
                    return { index: captchaIndex, method: 'short_field', element: captchaInput };
                }

                // الطريقة 3: البحث عن حقول محددة بأسمائها الصحيحة
                // البحث عن حقل الكابتشا بالاسم المحدد
                captchaInput = inputs.find(input =>
                    input.name === 'userCode' || // الاسم الصحيح لحقل الكابتشا
                    input.name === 'captcha_code' ||
                    input.name === 'security_code' ||
                    input.name === 'verification_code'
                );

                if (captchaInput) {
                    captchaIndex = inputs.indexOf(captchaInput);
                    console.log(`✅ وجد حقل الكابتشا بالاسم المحدد: index=${captchaIndex}, name="${captchaInput.name}"`);
                    return { index: captchaIndex, method: 'specific_name', element: captchaInput };
                }

                // البحث عن حقل البطاقة للتأكد من أننا في الصفحة الصحيحة
                const voucherInput = inputs.find(input => input.name === 'voucher');
                if (voucherInput) {
                    console.log(`✅ تأكيد وجود حقل البطاقة: name="${voucherInput.name}"`);

                    // إذا وجدنا حقل البطاقة، ابحث عن حقل الكابتشا في نفس النموذج
                    const form = voucherInput.closest('form');
                    if (form) {
                        const formInputs = Array.from(form.querySelectorAll('input[type="text"], input:not([type])'));
                        captchaInput = formInputs.find(input =>
                            input !== voucherInput &&
                            input.value === '' &&
                            input.offsetParent !== null &&
                            (input.maxLength <= 10 || !input.maxLength)
                        );

                        if (captchaInput) {
                            captchaIndex = inputs.indexOf(captchaInput);
                            console.log(`✅ وجد حقل الكابتشا في نفس النموذج: index=${captchaIndex}`);
                            return { index: captchaIndex, method: 'same_form', element: captchaInput };
                        }
                    }
                }

                // الطريقة 4: آخر حقل نص فارغ مرئي
                const emptyInputs = inputs.filter(input =>
                    input.type === 'text' &&
                    input.value === '' &&
                    input.offsetParent !== null &&
                    !input.disabled &&
                    !input.readOnly
                );

                if (emptyInputs.length > 0) {
                    captchaInput = emptyInputs[emptyInputs.length - 1];
                    captchaIndex = inputs.indexOf(captchaInput);
                    console.log(`✅ استخدام آخر حقل فارغ: index=${captchaIndex}`);
                    return { index: captchaIndex, method: 'last_empty', element: captchaInput };
                }

                console.log('❌ لم يتم العثور على حقل الكابتشا');
                return { index: -1, method: 'not_found', element: null };
            });

            if (captchaInputInfo.index !== -1) {
                console.log(`🎯 استخدام الطريقة: ${captchaInputInfo.method}`);

                // استخدام محدد مباشر بدلاً من nth-of-type
                const success = await page.evaluate((index, text) => {
                    const inputs = Array.from(document.querySelectorAll('input'));
                    const targetInput = inputs[index];

                    if (targetInput) {
                        console.log(`🎯 تعبئة الحقل: name="${targetInput.name}", id="${targetInput.id}", type="${targetInput.type}"`);
                        targetInput.value = '';
                        targetInput.focus();
                        targetInput.value = text;

                        // إطلاق أحداث للتأكد من التسجيل
                        targetInput.dispatchEvent(new Event('input', { bubbles: true }));
                        targetInput.dispatchEvent(new Event('change', { bubbles: true }));

                        return true;
                    }
                    return false;
                }, captchaInputInfo.index, captchaText);

                if (success) {
                    console.log(`✅ تم تعبئة كابتشا المرحلة الثانية بنجاح: ${captchaText}`);
                } else {
                    throw new Error('فشل في تعبئة حقل الكابتشا');
                }
            } else {
                throw new Error('لم يتم العثور على حقل الكابتشا في الصفحة الثانية');
            }

                // التحقق من أن الكابتشا تم تعبئتها بالفعل
                const verificationResult = await page.evaluate((index, expectedText) => {
                    const inputs = Array.from(document.querySelectorAll('input'));
                    const targetInput = inputs[index];

                    if (targetInput && targetInput.value === expectedText) {
                        console.log(`✅ تم التحقق: الكابتشا في المكان الصحيح`);
                        return { success: true, actualValue: targetInput.value };
                    } else {
                        console.log(`❌ خطأ في التحقق: القيمة الفعلية="${targetInput?.value}", المتوقعة="${expectedText}"`);
                        return { success: false, actualValue: targetInput?.value || 'null' };
                    }
                }, captchaInputInfo.index, captchaText);

                if (!verificationResult.success) {
                    console.log(`⚠️ تحذير: الكابتشا قد لا تكون في المكان الصحيح`);
                    console.log(`📊 القيمة الفعلية: "${verificationResult.actualValue}"`);
                    console.log(`📊 القيمة المتوقعة: "${captchaText}"`);
                }

                // انتظار قصير للتأكد من التعبئة
                await page.waitForTimeout(1000);

                await captchaSolver.cleanupImage(imagePath);

                // محاولة النقر على زر التأكيد النهائي
                console.log('النقر على زر التأكيد النهائي...');

                const finalConfirmButton = await page.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]'));
                    const confirmBtn = buttons.find(btn =>
                        btn.textContent?.includes('CONFIRMER') ||
                        btn.value?.includes('CONFIRMER') ||
                        btn.textContent?.includes('Confirmer') ||
                        btn.value?.includes('Confirmer') ||
                        btn.textContent?.includes('تأكيد') ||
                        btn.value?.includes('تأكيد') ||
                        btn.textContent?.includes('إرسال') ||
                        btn.value?.includes('إرسال')
                    );

                    if (confirmBtn) {
                        confirmBtn.click();
                        return true;
                    }
                    return false;
                });

                if (!finalConfirmButton) {
                    const fallbackButton = await page.evaluate(() => {
                        const submitBtn = document.querySelector('input[type="submit"], button[type="submit"]');
                        if (submitBtn) {
                            submitBtn.click();
                            return true;
                        }

                        const anyButton = document.querySelector('button');
                        if (anyButton) {
                            anyButton.click();
                            return true;
                        }
                        return false;
                    });

                    if (!fallbackButton) {
                        throw new Error('لم يتم العثور على أي زر للتأكيد النهائي');
                    }
                }

                console.log('✅ تم النقر على زر التأكيد النهائي - انتظار النتيجة...');

                // انتظار للتحقق من النتيجة
                await page.waitForTimeout(2000);

                // التحقق من وجود خطأ في الكابتشا أو البطاقة
                const errorCheck2 = await page.evaluate(() => {
                    const pageText = document.body.innerText.toLowerCase();
                    const pageHTML = document.body.innerHTML.toLowerCase();

                    console.log('🔍 فحص محتوى الصفحة للأخطاء...');
                    console.log('📄 محتوى الصفحة:', pageText.substring(0, 200));

                    // أولاً: التحقق من أخطاء البطاقة (أولوية عالية - لا تحتاج إعادة محاولة)
                    const voucherErrorKeywords = [
                        'vous devez saisir un voucher',
                        'voucher invalide',
                        'carte invalide',
                        'numéro de carte incorrect',
                        'code de carte invalide',
                        'carte expirée',
                        'carte déjà utilisée',
                        'يجب إدخال رقم البطاقة',
                        'بطاقة غير صحيحة',
                        'رقم البطاقة خاطئ'
                    ];

                    for (const keyword of voucherErrorKeywords) {
                        if (pageText.includes(keyword) || pageHTML.includes(keyword)) {
                            console.log(`❌ تم العثور على خطأ البطاقة: ${keyword}`);
                            return {
                                hasError: true,
                                errorType: 'voucher',
                                errorText: keyword,
                                shouldRetry: false
                            };
                        }
                    }

                    // ثانياً: التحقق من أخطاء الكابتشا (تحتاج إعادة محاولة)
                    const captchaErrorKeywords = [
                        'code incorrect',
                        'captcha incorrect',
                        'code invalide',
                        'code de sécurité incorrect',
                        'كود خاطئ',
                        'كابتشا خاطئة',
                        'رمز خاطئ'
                    ];

                    for (const keyword of captchaErrorKeywords) {
                        if (pageText.includes(keyword) || pageHTML.includes(keyword)) {
                            console.log(`⚠️ تم العثور على خطأ الكابتشا: ${keyword}`);
                            return {
                                hasError: true,
                                errorType: 'captcha',
                                errorText: keyword,
                                shouldRetry: true
                            };
                        }
                    }

                    // ثالثاً: التحقق من عدم الانتقال للصفحة التالية
                    const captchaStillExists = document.querySelector('img[src*="captcha"], img[src*="securimage"], .captcha img') !== null;
                    const voucherInputStillExists = document.querySelector('input[name*="carte"], input[name*="voucher"]') !== null;

                    if (captchaStillExists && voucherInputStillExists) {
                        console.log('⚠️ ما زلنا في نفس الصفحة - قد يكون خطأ كابتشا');

                        // ابحث عن رسائل خطأ مرئية في الصفحة
                        const errorElements = document.querySelectorAll('.error, .alert, .message, .notification, [class*="error"], [class*="alert"]');
                        for (const element of errorElements) {
                            const errorText = element.textContent.toLowerCase();
                            console.log(`🔍 رسالة خطأ موجودة: ${errorText}`);

                            // إذا وجدنا رسالة خطأ تتعلق بالبطاقة
                            for (const keyword of voucherErrorKeywords) {
                                if (errorText.includes(keyword)) {
                                    console.log(`❌ تأكيد خطأ البطاقة من رسالة الخطأ: ${keyword}`);
                                    return {
                                        hasError: true,
                                        errorType: 'voucher',
                                        errorText: keyword,
                                        shouldRetry: false
                                    };
                                }
                            }
                        }

                        // إذا لم نجد رسالة خطأ محددة، اعتبرها خطأ كابتشا
                        return {
                            hasError: true,
                            errorType: 'captcha',
                            errorText: 'still on voucher page - likely captcha error',
                            shouldRetry: true
                        };
                    }

                    // رابعاً: التحقق من رسائل النجاح
                    const successKeywords = [
                        'succès', 'success', 'réussi', 'completed', 'terminé',
                        'recharge effectuée', 'opération réussie',
                        'نجح', 'تم', 'اكتمل', 'مكتمل'
                    ];

                    for (const keyword of successKeywords) {
                        if (pageText.includes(keyword) || pageHTML.includes(keyword)) {
                            console.log(`✅ تم العثور على رسالة نجاح: ${keyword}`);
                            return {
                                hasError: false,
                                errorType: null,
                                errorText: null,
                                successText: keyword
                            };
                        }
                    }

                    console.log('ℹ️ لم يتم العثور على أخطاء أو رسائل نجاح واضحة');
                    return {
                        hasError: false,
                        errorType: null,
                        errorText: null,
                        successText: 'operation completed'
                    };
                });

                if (errorCheck2.hasError) {
                    console.log(`❌ خطأ في المرحلة الثانية: ${errorCheck2.errorText} (نوع: ${errorCheck2.errorType})`);

                    if (errorCheck2.errorType === 'voucher') {
                        // خطأ في البطاقة - إنهاء العملية فوراً بدون إعادة محاولة
                        console.log('💳 خطأ في رقم البطاقة - إنهاء العملية فوراً');
                        await captchaSolver.cleanupImage(imagePath);

                        // سيتم حفظ العملية الفاشلة في catch block الخارجي

                        // رمي خطأ مباشرة للخروج من الحلقة والدالة
                        throw new Error(`خطأ في البطاقة: ${errorCheck2.errorText}`);

                    } else if (errorCheck2.shouldRetry && captchaAttempt2 < maxCaptchaRetries) {
                        console.log('🔄 إعادة المحاولة للكابتشا...');
                        captchaAttempt2++;
                        continue;
                    } else {
                        // انتهت المحاولات للكابتشا
                        console.log(`❌ انتهت محاولات الكابتشا بعد ${maxCaptchaRetries} محاولات`);
                        await captchaSolver.cleanupImage(imagePath);
                        // حفظ العملية الفاشلة قبل الخروج
                        const failedOperationData = {
                            serviceType: serviceType,
                            phoneNumber: phoneNumber,
                            voucherCode: voucherCode,
                            success: false,
                            message: `فشل في حل كابتشا المرحلة الثانية بعد ${maxCaptchaRetries} محاولات`,
                            errorType: 'captcha',
                            extractedInfo: null,
                            pageContent: await page.content(),
                            details: {
                                stage1_completed: stage1Success,
                                stage2_completed: false,
                                captcha_attempts_stage1: captchaAttempt - 1,
                                captcha_attempts_stage2: maxCaptchaRetries,
                                service_url: serviceUrls[serviceType],
                                page_text_preview: (await page.content()).substring(0, 500)
                            },
                            ipAddress: req.ip || req.socket.remoteAddress,
                            userAgent: req.get('User-Agent')
                        };

                        try {
                            const savedId = await database.saveOperation(failedOperationData);
                            console.log(`✅ تم حفظ العملية الفاشلة في قاعدة البيانات برقم: ${savedId}`);
                        } catch (dbError) {
                            console.error('❌ خطأ في حفظ العملية الفاشلة:', dbError.message);
                        }

                        throw new Error(`فشل في حل كابتشا المرحلة الثانية بعد ${maxCaptchaRetries} محاولات`);
                    }
                } else {
                    console.log(`✅ نجح في المرحلة الثانية - تمت العملية بنجاح: ${errorCheck2.successText || 'completed'}`);
                    stage2Success = true;
                }

            } catch (error) {
                console.log(`⚠️ خطأ في المحاولة ${captchaAttempt2}: ${error.message}`);
                if (captchaAttempt2 < maxCaptchaRetries) {
                    console.log('🔄 إعادة المحاولة...');
                    captchaAttempt2++;
                    await page.waitForTimeout(2000); // انتظار قبل إعادة المحاولة
                } else {
                    // سيتم حفظ العملية الفاشلة في catch block الخارجي

                    throw new Error(`فشل في حل كابتشا المرحلة الثانية بعد ${maxCaptchaRetries} محاولات: ${error.message}`);
                }
            }
        }

        // إذا وصلنا هنا، فقد نجحت المرحلة الثانية
        console.log('✅ تمت المرحلة الثانية بنجاح');

        // تم دمج النقر على الزر في حلقة إعادة المحاولة أعلاه
        
        console.log('انتظار نتيجة العملية...');
        await page.waitForTimeout(2000);

        // Check for success/error messages with enhanced detection
        console.log('فحص نتيجة العملية النهائية...');
        const result = await page.evaluate(() => {
            const pageText = document.body.innerText.toLowerCase();
            const pageHTML = document.body.innerHTML.toLowerCase();
            const fullPageContent = document.body.innerText; // المحتوى الكامل للصفحة

            // استخراج معلومات مفيدة من الصفحة
            function extractPageInfo(content) {
                const info = {};

                // البحث عن رقم العملية
                const operationMatch = content.match(/n°\s*d[''']opération\s*[:\s]*([a-z0-9]+)/i);
                if (operationMatch) {
                    info.operationNumber = operationMatch[1];
                }

                // البحث عن رقم الهاتف
                const phoneMatch = content.match(/n°\s*tél\s*[:\s]*([0-9]+)/i);
                if (phoneMatch) {
                    info.phoneNumber = phoneMatch[1];
                }

                // البحث عن رقم العميل
                const clientMatch = content.match(/n°\s*client\s*[:\s]*([0-9]+)/i);
                if (clientMatch) {
                    info.clientNumber = clientMatch[1];
                }

                // البحث عن التاريخ والوقت
                const dateMatch = content.match(/date\s*[:\s]*([0-9\-\/]+)/i);
                if (dateMatch) {
                    info.date = dateMatch[1];
                }

                const timeMatch = content.match(/heure\s*[:\s]*([0-9:]+)/i);
                if (timeMatch) {
                    info.time = timeMatch[1];
                }

                return info;
            }

            const extractedInfo = extractPageInfo(fullPageContent);

            // Check for specific error messages first
            const specificErrors = [
                {
                    keywords: ['vous devez saisir un voucher', 'يجب إدخال رقم البطاقة'],
                    message: 'خطأ: يجب إدخال رقم بطاقة صحيح',
                    type: 'voucher_required'
                },
                {
                    keywords: ['le numéro de téléphone est incorrect', 'رقم الهاتف غير صحيح'],
                    message: 'خطأ: رقم الهاتف غير صحيح',
                    type: 'phone_incorrect'
                },
                {
                    keywords: ['carte invalide', 'voucher invalide', 'بطاقة غير صحيحة'],
                    message: 'خطأ: رقم البطاقة غير صحيح أو منتهي الصلاحية',
                    type: 'voucher_invalid'
                },
                {
                    keywords: ['code incorrect', 'captcha incorrect', 'كود خاطئ'],
                    message: 'خطأ: كود التحقق غير صحيح',
                    type: 'captcha_incorrect'
                },
                {
                    keywords: ['solde insuffisant', 'رصيد غير كافي'],
                    message: 'خطأ: الرصيد غير كافي',
                    type: 'insufficient_balance'
                },
                {
                    keywords: ['déjà utilisé', 'already used', 'مستخدم مسبقاً'],
                    message: 'خطأ: البطاقة مستخدمة مسبقاً',
                    type: 'already_used'
                }
            ];

            // Check for specific errors
            for (const error of specificErrors) {
                for (const keyword of error.keywords) {
                    if (pageText.includes(keyword) || pageHTML.includes(keyword)) {
                        return {
                            success: false,
                            message: error.message,
                            errorType: error.type,
                            pageText: pageText.substring(0, 500),
                            fullPageContent: fullPageContent,
                            extractedInfo: extractedInfo
                        };
                    }
                }
            }

            // Check for success indicators
            const successKeywords = [
                'recharge effectuée', 'recharge réussie', 'succès', 'success', 'réussi',
                'تمت التعبئة', 'نجحت العملية', 'تم بنجاح', 'completed', 'terminé'
            ];

            for (const keyword of successKeywords) {
                if (pageText.includes(keyword) || pageHTML.includes(keyword)) {
                    return {
                        success: true,
                        message: 'تمت عملية التعبئة بنجاح ✅',
                        errorType: null,
                        pageText: pageText.substring(0, 500),
                        fullPageContent: fullPageContent,
                        extractedInfo: extractedInfo
                    };
                }
            }

            // Check if we're on a result page (no forms)
            const hasForm = document.querySelector('form') !== null;
            const hasVoucherInputs = document.querySelectorAll('input[name*="carte"], input[name*="voucher"]').length > 0;
            const hasCaptcha = document.querySelector('img[src*="captcha"]') !== null;

            if (!hasForm && !hasVoucherInputs && !hasCaptcha) {
                // Likely a result page - check for any indication
                if (pageText.includes('algérie télécom') && pageText.length < 1000) {
                    return {
                        success: false,
                        message: 'العملية لم تكتمل - يرجى التحقق من البيانات المدخلة',
                        errorType: 'incomplete',
                        pageText: pageText.substring(0, 500),
                        fullPageContent: fullPageContent,
                        extractedInfo: extractedInfo
                    };
                } else {
                    return {
                        success: true,
                        message: 'تمت العملية - يرجى التحقق من النتيجة',
                        errorType: null,
                        pageText: pageText.substring(0, 500),
                        fullPageContent: fullPageContent,
                        extractedInfo: extractedInfo
                    };
                }
            }

            // Still on form page - likely an error
            return {
                success: false,
                message: 'العملية لم تكتمل - خطأ غير محدد',
                errorType: 'unknown',
                pageText: pageText.substring(0, 500),
                fullPageContent: fullPageContent,
                extractedInfo: extractedInfo
            };
        });

        console.log(`📊 نتيجة العملية: ${result.success ? 'نجح' : 'فشل'}`);
        console.log(`📋 نوع الخطأ: ${result.errorType || 'لا يوجد'}`);
        console.log(`📄 محتوى الصفحة: ${result.pageText}...`);

        let success = result.success;
        let message = result.message;

        await browser.close();

        // حفظ العملية في قاعدة البيانات
        try {
            console.log('💾 بدء حفظ العملية في قاعدة البيانات...');

            const operationData = {
                serviceType: serviceType,
                phoneNumber: phoneNumber,
                voucherCode: voucherCode,
                success: success,
                message: message,
                errorType: result.errorType || null,
                extractedInfo: result.extractedInfo || null,
                pageContent: result.fullPageContent || null,
                details: {
                    stage1_completed: stage1Success,
                    stage2_completed: stage2Success,
                    captcha_attempts_stage1: captchaAttempt - 1,
                    captcha_attempts_stage2: captchaAttempt2 - 1,
                    service_url: serviceUrls[serviceType],
                    page_text_preview: result.pageText || null
                },
                ipAddress: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent')
            };

            console.log('📋 بيانات العملية للحفظ:', {
                serviceType: operationData.serviceType,
                phoneNumber: operationData.phoneNumber,
                success: operationData.success,
                message: operationData.message
            });

            const savedId = await database.saveOperation(operationData);
            console.log(`✅ تم حفظ العملية في قاعدة البيانات برقم: ${savedId}`);
        } catch (dbError) {
            console.error('❌ خطأ في حفظ العملية في قاعدة البيانات:', dbError.message);
            console.error('❌ تفاصيل الخطأ:', dbError);
        }

        res.json({
            success: success,
            message: message,
            serviceType: serviceType.toUpperCase(),
            phoneNumber: phoneNumber,
            voucherCode: voucherCode.substring(0, 4) + '****' + voucherCode.substring(voucherCode.length - 4), // إخفاء جزء من رقم البطاقة
            errorType: result.errorType || null,
            timestamp: new Date().toISOString(),
            pageContent: result.fullPageContent || null, // محتوى الصفحة الكامل
            extractedInfo: result.extractedInfo || null, // المعلومات المستخرجة من الصفحة
            details: {
                stage1_completed: stage1Success,
                stage2_completed: stage2Success,
                captcha_attempts_stage1: captchaAttempt - 1,
                captcha_attempts_stage2: captchaAttempt2 - 1,
                service_url: serviceUrls[serviceType],
                page_text_preview: result.pageText || null
            }
        });

    } catch (error) {
        console.error('خطأ في عملية التعبئة:', error);

        // حفظ العملية الفاشلة في قاعدة البيانات
        try {
            console.log('💾 حفظ العملية الفاشلة في قاعدة البيانات...');

            const serviceUrls = {
                '4g': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g',
                'adsl': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
            };

            const failedOperationData = {
                serviceType: serviceType,
                phoneNumber: phoneNumber,
                voucherCode: voucherCode,
                success: false,
                message: 'خطأ في عملية التعبئة: ' + error.message,
                errorType: 'system_error',
                extractedInfo: null,
                pageContent: null,
                details: {
                    stage1_completed: stage1Success,
                    stage2_completed: stage2Success,
                    captcha_attempts_stage1: captchaAttempt - 1,
                    captcha_attempts_stage2: captchaAttempt2 - 1,
                    service_url: serviceUrls[serviceType],
                    page_text_preview: null
                },
                ipAddress: req.ip || req.socket.remoteAddress,
                userAgent: req.get('User-Agent')
            };

            const savedId = await database.saveOperation(failedOperationData);
            console.log(`✅ تم حفظ العملية الفاشلة في قاعدة البيانات برقم: ${savedId}`);
        } catch (dbError) {
            console.error('❌ خطأ في حفظ العملية الفاشلة:', dbError.message);
        }

        if (browser) {
            await browser.close();
        }

        res.status(500).json({
            success: false,
            message: 'حدث خطأ في النظام: ' + error.message
        });
    }
});

// Route for recharging voucher using API method (without browser)
app.post('/recharge-api', async (req, res) => {
    const { phoneNumber, voucherCode, serviceType = '4g' } = req.body;

    if (!phoneNumber || !voucherCode) {
        return res.status(400).json({
            success: false,
            message: 'رقم الهاتف ورقم البطاقة مطلوبان'
        });
    }

    // التحقق من نوع الخدمة
    if (!['4g', 'adsl'].includes(serviceType)) {
        return res.status(400).json({
            success: false,
            message: 'نوع الخدمة يجب أن يكون 4g أو adsl'
        });
    }

    try {
        console.log(`🚀 بدء التعبئة عبر API - ${serviceType.toUpperCase()}`);
        console.log(`📱 رقم الهاتف: ${phoneNumber}`);
        console.log(`💳 رقم البطاقة: ${voucherCode.substring(0, 4)}****${voucherCode.substring(voucherCode.length - 4)}`);

        // استخدام API Client بدلاً من Puppeteer
        const result = await apiClient.rechargeVoucher(phoneNumber, voucherCode, serviceType);

        // حفظ العملية في قاعدة البيانات
        try {
            console.log('💾 بدء حفظ العملية في قاعدة البيانات...');

            const operationData = {
                serviceType: serviceType,
                phoneNumber: phoneNumber,
                voucherCode: voucherCode,
                success: result.success,
                message: result.message,
                errorType: result.errorType || null,
                extractedInfo: result.extractedInfo || null,
                pageContent: result.pageContent || null,
                details: {
                    method: 'api_client',
                    stage1_completed: true, // API method doesn't have separate stages
                    stage2_completed: result.success,
                    captcha_attempts_stage1: 1,
                    captcha_attempts_stage2: 1,
                    service_url: serviceType === '4g' ?
                        'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g' :
                        'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
                },
                ipAddress: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent')
            };

            const savedId = await database.saveOperation(operationData);
            console.log(`✅ تم حفظ العملية في قاعدة البيانات برقم: ${savedId}`);
        } catch (dbError) {
            console.error('❌ خطأ في حفظ العملية في قاعدة البيانات:', dbError.message);
        }

        // إرجاع النتيجة
        res.json({
            success: result.success,
            message: result.message,
            method: 'API Client (بدون متصفح)',
            serviceType: serviceType.toUpperCase(),
            phoneNumber: phoneNumber,
            voucherCode: voucherCode.substring(0, 4) + '****' + voucherCode.substring(voucherCode.length - 4),
            errorType: result.errorType || null,
            timestamp: new Date().toISOString(),
            pageContent: result.pageContent || null,
            extractedInfo: result.extractedInfo || null
        });

    } catch (error) {
        console.error('❌ خطأ في عملية التعبئة عبر API:', error.message);

        // حفظ العملية الفاشلة في قاعدة البيانات
        try {
            const failedOperationData = {
                serviceType: serviceType,
                phoneNumber: phoneNumber,
                voucherCode: voucherCode,
                success: false,
                message: 'خطأ في عملية التعبئة عبر API: ' + error.message,
                errorType: 'api_system_error',
                extractedInfo: null,
                pageContent: null,
                details: {
                    method: 'api_client',
                    stage1_completed: false,
                    stage2_completed: false,
                    captcha_attempts_stage1: 0,
                    captcha_attempts_stage2: 0,
                    service_url: serviceType === '4g' ?
                        'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g' :
                        'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
                },
                ipAddress: req.ip || req.socket.remoteAddress,
                userAgent: req.get('User-Agent')
            };

            const savedId = await database.saveOperation(failedOperationData);
            console.log(`✅ تم حفظ العملية الفاشلة في قاعدة البيانات برقم: ${savedId}`);
        } catch (dbError) {
            console.error('❌ خطأ في حفظ العملية الفاشلة:', dbError.message);
        }

        res.status(500).json({
            success: false,
            message: 'حدث خطأ في النظام: ' + error.message,
            method: 'API Client (بدون متصفح)'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'خدمة التعبئة الآلية تعمل بشكل طبيعي' });
});

// Check AZCaptcha API key
app.get('/check-captcha-api', async (req, res) => {
    try {
        if (!process.env.AZCAPTCHA_API_KEY || process.env.AZCAPTCHA_API_KEY === 'your_azcaptcha_api_key_here') {
            return res.status(400).json({
                success: false,
                message: 'مفتاح AZCaptcha API غير مُعرَّف. يرجى تحديث ملف .env'
            });
        }

        const isValid = await captchaSolver.validateApiKey();
        res.json({
            success: isValid,
            message: isValid ? 'مفتاح AZCaptcha API صحيح وجاهز للاستخدام' : 'مفتاح AZCaptcha API غير صحيح'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'خطأ في التحقق من مفتاح API: ' + error.message
        });
    }
});

// Dashboard routes
app.get('/dashboard', (req, res) => {
    res.sendFile(__dirname + '/public/dashboard.html');
});

// API للحصول على العمليات
app.get('/api/operations', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;

        const filters = {
            serviceType: req.query.serviceType,
            success: req.query.success !== undefined ? req.query.success === 'true' : undefined,
            dateFrom: req.query.dateFrom,
            dateTo: req.query.dateTo,
            phoneNumber: req.query.phoneNumber
        };

        const operations = await database.getOperations(limit, offset, filters);
        res.json({
            success: true,
            data: operations,
            pagination: {
                page: page,
                limit: limit,
                offset: offset
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب العمليات: ' + error.message
        });
    }
});

// API للحصول على عملية محددة
app.get('/api/operations/:id', async (req, res) => {
    try {
        const operation = await database.getOperationById(req.params.id);
        if (operation) {
            res.json({
                success: true,
                data: operation
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'العملية غير موجودة'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب العملية: ' + error.message
        });
    }
});

// API للحصول على الإحصائيات العامة
app.get('/api/stats/overall', async (req, res) => {
    try {
        const stats = await database.getOverallStats();
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الإحصائيات: ' + error.message
        });
    }
});

// API للحصول على الإحصائيات اليومية
app.get('/api/stats/daily', async (req, res) => {
    try {
        const days = parseInt(req.query.days) || 30;
        const stats = await database.getDailyStats(days);
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الإحصائيات اليومية: ' + error.message
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
    console.log(`📱 لاستخدام الخدمة، أرسل POST request إلى: http://localhost:${PORT}/recharge`);
    console.log(`📋 مثال على البيانات المطلوبة: {"phoneNumber": "0123456789", "voucherCode": "1234567890123456"}`);
});
