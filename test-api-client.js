const axios = require('axios');

// Test script for the new API client method
async function testAPIClient() {
    const baseURL = 'http://localhost:3000';
    
    console.log('🧪 بدء اختبار النظام الجديد (API Client)...\n');
    
    try {
        // Test 1: Health check
        console.log('1️⃣ اختبار حالة الخادم...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('✅ الخادم يعمل:', healthResponse.data.message);
        
        // Test 2: Check AZCaptcha API key
        console.log('\n2️⃣ اختبار مفتاح AZCaptcha...');
        const captchaResponse = await axios.get(`${baseURL}/check-captcha-api`);
        console.log(captchaResponse.data.success ? '✅' : '❌', captchaResponse.data.message);
        
        if (!captchaResponse.data.success) {
            console.log('⚠️ يرجى إعداد مفتاح AZCaptcha في ملف .env');
            return;
        }
        
        // Test 3: Compare both methods
        console.log('\n3️⃣ مقارنة الطرق المختلفة...');
        
        console.log('\n📊 الطرق المتاحة:');
        console.log('   🌐 الطريقة الأصلية (Puppeteer): /recharge');
        console.log('   ⚡ الطريقة الجديدة (API Client): /recharge-api');
        
        // Test data
        const testData4G = {
            serviceType: '4g',
            phoneNumber: '0123456789',
            voucherCode: '1234567890123456'
        };

        const testDataADSL = {
            serviceType: 'adsl',
            phoneNumber: '0123456789',
            voucherCode: '1234567890123456'
        };
        
        console.log('\n⚠️ هذا اختبار بيانات وهمية - لن يتم تنفيذ عملية حقيقية');
        console.log('⏭️ تم تخطي الاختبار الفعلي لتجنب استهلاك الرصيد');

        // Uncomment to test the actual API calls
        /*
        console.log('\n🔄 اختبار الطريقة الجديدة (API Client) - خدمة 4G...');
        try {
            const apiResponse4G = await axios.post(`${baseURL}/recharge-api`, testData4G, {
                timeout: 120000 // 2 minutes timeout
            });
            console.log('📊 نتيجة API Client (4G):', apiResponse4G.data.success ? '✅ نجح' : '❌ فشل');
            console.log('📝 الرسالة:', apiResponse4G.data.message);
            if (apiResponse4G.data.extractedInfo) {
                console.log('📋 المعلومات المستخرجة:', apiResponse4G.data.extractedInfo);
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار API Client (4G):', error.message);
        }

        console.log('\n🔄 اختبار الطريقة الجديدة (API Client) - خدمة ADSL...');
        try {
            const apiResponseADSL = await axios.post(`${baseURL}/recharge-api`, testDataADSL, {
                timeout: 120000 // 2 minutes timeout
            });
            console.log('📊 نتيجة API Client (ADSL):', apiResponseADSL.data.success ? '✅ نجح' : '❌ فشل');
            console.log('📝 الرسالة:', apiResponseADSL.data.message);
            if (apiResponseADSL.data.extractedInfo) {
                console.log('📋 المعلومات المستخرجة:', apiResponseADSL.data.extractedInfo);
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار API Client (ADSL):', error.message);
        }
        */

        console.log('\n🎉 انتهى الاختبار بنجاح!');
        console.log('\n📋 النظام جاهز للاستخدام:');
        console.log(`   - واجهة الويب: ${baseURL}`);
        console.log(`   - API التعبئة (الأصلي): ${baseURL}/recharge`);
        console.log(`   - API التعبئة (الجديد): ${baseURL}/recharge-api`);
        console.log(`   - فحص الحالة: ${baseURL}/health`);
        console.log(`   - فحص AZCaptcha: ${baseURL}/check-captcha-api`);
        console.log(`   - لوحة المراقبة: ${baseURL}/dashboard`);
        
        console.log('\n🔧 الخدمات المدعومة:');
        console.log('   - 4G LTE: serviceType: "4g"');
        console.log('   - ADSL: serviceType: "adsl"');
        
        console.log('\n⚡ مميزات الطريقة الجديدة (API Client):');
        console.log('   ✅ أسرع في التنفيذ (بدون تشغيل متصفح)');
        console.log('   ✅ استهلاك أقل للذاكرة والمعالج');
        console.log('   ✅ أكثر استقراراً');
        console.log('   ✅ أقل عرضة للكشف');
        console.log('   ✅ يدعم نفس الميزات (حل الكابتشا، استخراج المعلومات)');
        
        console.log('\n📝 مثال على الاستخدام:');
        console.log('curl -X POST http://localhost:3000/recharge-api \\');
        console.log('  -H "Content-Type: application/json" \\');
        console.log('  -d \'{"serviceType": "4g", "phoneNumber": "0123456789", "voucherCode": "1234567890123456"}\'');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 تأكد من تشغيل الخادم: npm start');
        }
    }
}

// Function to compare performance between methods
async function comparePerformance() {
    console.log('\n🏁 مقارنة الأداء بين الطرق...');
    
    const testData = {
        serviceType: '4g',
        phoneNumber: '0123456789',
        voucherCode: '1234567890123456'
    };
    
    console.log('\n⚠️ هذا اختبار نظري - لن يتم تنفيذ عمليات حقيقية');
    
    console.log('\n📊 مقارنة نظرية:');
    console.log('┌─────────────────────┬──────────────────┬──────────────────┐');
    console.log('│ المعيار             │ Puppeteer        │ API Client       │');
    console.log('├─────────────────────┼──────────────────┼──────────────────┤');
    console.log('│ وقت التنفيذ         │ 30-60 ثانية      │ 10-20 ثانية      │');
    console.log('│ استهلاك الذاكرة     │ 100-200 MB       │ 10-20 MB         │');
    console.log('│ استهلاك المعالج     │ عالي             │ منخفض            │');
    console.log('│ الاستقرار          │ متوسط            │ عالي             │');
    console.log('│ قابلية الكشف       │ متوسطة           │ منخفضة           │');
    console.log('│ سهولة الصيانة      │ معقدة            │ بسيطة            │');
    console.log('└─────────────────────┴──────────────────┴──────────────────┘');
    
    console.log('\n💡 التوصية: استخدم API Client للأداء الأفضل والاستقرار الأعلى');
}

// Run the test
if (require.main === module) {
    testAPIClient().then(() => {
        return comparePerformance();
    });
}

module.exports = { testAPIClient, comparePerformance };
