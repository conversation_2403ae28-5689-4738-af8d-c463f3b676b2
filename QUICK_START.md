# البدء السريع - حل مشكلة API

## المشكلة
الطريقة الحالية لا تعمل لأنها لم تكن مبنية على تحليل حقيقي للموقع.

## الحل
أداة مراقبة تحلل العمليات الحقيقية وتبني API client صحيح.

## البدء السريع

### للمستخدمين على Windows
```cmd
# انقر مرتين على الملف
start-monitoring.bat
```

### للجميع
```bash
# بدء المراقبة
npm run monitor-api

# أو
node monitor-and-build.js monitor
```

## الخطوات

### 1. بدء المراقبة
- شغل الأمر أعلاه
- اختر نوع الخدمة (4G أو ADSL)
- انتظر فتح المتصفح

### 2. العملية اليدوية
في المتصفح المفتوح:
1. املأ رقم الهاتف (استخدم رقم حقيقي)
2. حل الكابتشا الأولى
3. اضغط تأكيد
4. املأ رقم البطاقة (استخدم رقم حقيقي إن أمكن)
5. حل الكابتشا الثانية
6. اضغط تأكيد نهائي
7. انتظر النتيجة

### 3. إنهاء المراقبة
- في Terminal، اضغط **Ctrl+C**
- ستُحفظ البيانات تلقائياً

### 4. تحليل البيانات
```bash
# تحليل البيانات المسجلة
npm run analyze-api

# أو
node monitor-and-build.js analyze
```

## النتائج

ستحصل على:
- **`api_analysis.json`** - البيانات الخام
- **`api_detailed_log.txt`** - تقرير مفصل
- **`real-api-client.js`** - API client جديد
- **`API_ANALYSIS_RESULTS.md`** - ملخص النتائج

## استخدام API الجديد

```javascript
const RealAPIClient = require('./real-api-client');
const captchaSolver = new AZCaptchaSolver(process.env.AZCAPTCHA_API_KEY);
const client = new RealAPIClient(captchaSolver);

// استخدام API client الجديد
await client.rechargeVoucher(phoneNumber, voucherCode, serviceType);
```

## نصائح مهمة

### ✅ للحصول على أفضل نتائج:
- استخدم بيانات حقيقية
- أكمل العملية كاملة
- لا تغلق المتصفح مبكراً
- اضغط Ctrl+C في Terminal فقط

### ⚠️ إذا فشلت:
- كرر مع بيانات مختلفة
- جرب في وقت مختلف
- تأكد من استقرار الاتصال
- راجع الملفات المُنتجة (ستحصل على بيانات مفيدة حتى لو فشلت)

## الأوامر السريعة

```bash
# عرض القائمة
node monitor-and-build.js

# بدء المراقبة
npm run monitor-api

# تحليل البيانات
npm run analyze-api

# عرض التعليمات
npm run monitor-help

# فحص الملفات
node monitor-and-build.js check
```

## الدعم

### الوثائق الكاملة:
- `API_MONITORING_GUIDE.md` - دليل شامل
- `MONITORING_SOLUTION.md` - شرح مفصل للحل
- `README.md` - معلومات عامة

### المساعدة السريعة:
```bash
node monitor-and-build.js help
```

---

## ابدأ الآن!

**Windows:** انقر على `start-monitoring.bat`  
**الجميع:** `npm run monitor-api`

🎉 **ستحصل على API يعمل 100%!**
