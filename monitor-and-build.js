#!/usr/bin/env node

/**
 * أداة شاملة لمراقبة وبناء API الجزائر تيليكوم
 * تقوم بفتح المتصفح، مراقبة العمليات، وبناء API client حقيقي
 */

const { APIMonitor } = require('./api-monitor');
const fs = require('fs');
const path = require('path');

const COLORS = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

function printHeader(title) {
    console.log('\n' + '='.repeat(80));
    colorLog('cyan', `🔍 ${title}`);
    console.log('='.repeat(80));
}

function printInstructions() {
    printHeader('مراقب API - الجزائر تيليكوم');
    
    colorLog('yellow', '\n📋 هذه الأداة ستقوم بـ:');
    console.log('1. فتح المتصفح على موقع الجزائر تيليكوم');
    console.log('2. مراقبة جميع طلبات HTTP التي تحدث');
    console.log('3. تسجيل البيانات المرسلة والمستقبلة');
    console.log('4. بناء API client حقيقي بناءً على التحليل');
    
    colorLog('yellow', '\n🎯 ما عليك فعله:');
    console.log('1. انتظر حتى يفتح المتصفح');
    console.log('2. قم بعملية التعبئة يدوياً كما تفعل عادة');
    console.log('3. املأ رقم الهاتف');
    console.log('4. حل الكابتشا الأولى');
    console.log('5. اضغط تأكيد');
    console.log('6. املأ رقم البطاقة');
    console.log('7. حل الكابتشا الثانية');
    console.log('8. اضغط تأكيد نهائي');
    console.log('9. انتظر النتيجة');
    console.log('10. اضغط Ctrl+C هنا لإنهاء المراقبة');
    
    colorLog('green', '\n✅ النتيجة:');
    console.log('- ملف JSON يحتوي على جميع البيانات');
    console.log('- تقرير مفصل بالطلبات المهمة');
    console.log('- API client جديد مبني على البيانات الحقيقية');
    
    colorLog('red', '\n⚠️ مهم:');
    console.log('- استخدم بيانات حقيقية للحصول على أفضل نتائج');
    console.log('- لا تغلق المتصفح حتى تكتمل العملية');
    console.log('- تأكد من اتصال الإنترنت');
}

async function selectServiceType() {
    return new Promise((resolve) => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        colorLog('blue', '\n🌐 اختر نوع الخدمة:');
        console.log('1. 4G LTE');
        console.log('2. ADSL');
        
        rl.question('\nأدخل رقم الخيار (1 أو 2): ', (answer) => {
            rl.close();
            const serviceType = answer === '2' ? 'adsl' : '4g';
            resolve(serviceType);
        });
    });
}

async function confirmStart() {
    return new Promise((resolve) => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        colorLog('yellow', '\n🚀 هل أنت مستعد لبدء المراقبة؟');
        rl.question('اكتب "نعم" أو "y" للمتابعة: ', (answer) => {
            rl.close();
            const confirmed = ['نعم', 'y', 'yes', 'Y', 'YES'].includes(answer.trim());
            resolve(confirmed);
        });
    });
}

async function startMonitoringProcess() {
    try {
        printInstructions();
        
        // اختيار نوع الخدمة
        const serviceType = await selectServiceType();
        colorLog('green', `✅ تم اختيار خدمة: ${serviceType.toUpperCase()}`);
        
        // تأكيد البدء
        const confirmed = await confirmStart();
        if (!confirmed) {
            colorLog('yellow', '⏹️ تم إلغاء العملية');
            return;
        }
        
        colorLog('cyan', '\n🔄 بدء المراقبة...');
        colorLog('yellow', '📱 سيتم فتح المتصفح خلال ثوانٍ...');
        
        // بدء المراقبة
        const monitor = new APIMonitor();
        await monitor.startMonitoring(serviceType);
        
    } catch (error) {
        colorLog('red', `❌ خطأ في المراقبة: ${error.message}`);
        console.error(error);
    }
}

async function analyzeExistingData() {
    const logFile = path.join(__dirname, 'api_analysis.json');
    
    if (!fs.existsSync(logFile)) {
        colorLog('red', '❌ لا يوجد بيانات محفوظة للتحليل');
        colorLog('yellow', '💡 قم بتشغيل المراقبة أولاً: node monitor-and-build.js');
        return;
    }
    
    colorLog('cyan', '🔍 تحليل البيانات المحفوظة...');
    
    try {
        const monitor = new APIMonitor();
        await monitor.analyzeAndGenerateAPI();
        
        colorLog('green', '\n✅ تم التحليل بنجاح!');
        colorLog('blue', '\n📁 الملفات المُنشأة:');
        console.log('- api_analysis.json (البيانات الخام)');
        console.log('- api_detailed_log.txt (التقرير المفصل)');
        console.log('- real-api-client.js (API client الجديد)');
        console.log('- API_ANALYSIS_RESULTS.md (ملخص النتائج)');
        
    } catch (error) {
        colorLog('red', `❌ خطأ في التحليل: ${error.message}`);
    }
}

function showMenu() {
    printHeader('مراقب وباني API - الجزائر تيليكوم');
    
    colorLog('blue', '\n📋 الخيارات المتاحة:');
    console.log('1. بدء مراقبة جديدة (monitor)');
    console.log('2. تحليل البيانات المحفوظة (analyze)');
    console.log('3. عرض التعليمات (help)');
    console.log('4. فحص الملفات الموجودة (check)');
    
    console.log('\n💡 الاستخدام:');
    console.log('node monitor-and-build.js [monitor|analyze|help|check]');
    console.log('أو ببساطة: node monitor-and-build.js');
}

function checkExistingFiles() {
    colorLog('cyan', '\n📁 فحص الملفات الموجودة:');
    
    const files = [
        'api_analysis.json',
        'api_detailed_log.txt', 
        'real-api-client.js',
        'API_ANALYSIS_RESULTS.md'
    ];
    
    files.forEach(file => {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const size = (stats.size / 1024).toFixed(2);
            const date = stats.mtime.toLocaleString('ar-DZ');
            colorLog('green', `✅ ${file} (${size} KB - ${date})`);
        } else {
            colorLog('red', `❌ ${file} - غير موجود`);
        }
    });
}

function showHelp() {
    printHeader('دليل الاستخدام');
    
    colorLog('yellow', '\n🎯 الهدف:');
    console.log('هذه الأداة تقوم بمراقبة العمليات اليدوية على موقع الجزائر تيليكوم');
    console.log('وتسجيل جميع طلبات HTTP لبناء API client حقيقي يعمل بنفس الطريقة');
    
    colorLog('yellow', '\n📋 الخطوات:');
    console.log('1. تشغيل المراقبة: node monitor-and-build.js monitor');
    console.log('2. القيام بالعملية يدوياً في المتصفح المفتوح');
    console.log('3. إنهاء المراقبة بـ Ctrl+C');
    console.log('4. تحليل البيانات: node monitor-and-build.js analyze');
    console.log('5. استخدام API client الجديد');
    
    colorLog('yellow', '\n🔧 الأوامر:');
    console.log('monitor  - بدء مراقبة جديدة');
    console.log('analyze  - تحليل البيانات المحفوظة');
    console.log('check    - فحص الملفات الموجودة');
    console.log('help     - عرض هذه التعليمات');
    
    colorLog('yellow', '\n📁 الملفات المُنتجة:');
    console.log('api_analysis.json      - البيانات الخام (JSON)');
    console.log('api_detailed_log.txt   - تقرير مفصل (نص)');
    console.log('real-api-client.js     - API client جديد (JavaScript)');
    console.log('API_ANALYSIS_RESULTS.md - ملخص النتائج (Markdown)');
}

// تشغيل الأداة
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'menu';
    
    switch (command.toLowerCase()) {
        case 'monitor':
            await startMonitoringProcess();
            break;
            
        case 'analyze':
            await analyzeExistingData();
            break;
            
        case 'check':
            checkExistingFiles();
            break;
            
        case 'help':
            showHelp();
            break;
            
        default:
            showMenu();
            break;
    }
}

// تشغيل الأداة إذا تم استدعاؤها مباشرة
if (require.main === module) {
    main().catch(error => {
        colorLog('red', `❌ خطأ: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    startMonitoringProcess,
    analyzeExistingData,
    checkExistingFiles,
    showHelp
};
