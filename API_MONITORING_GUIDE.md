# دليل مراقبة وبناء API الحقيقي - الجزائر تيليكوم

## نظرة عامة

هذا الدليل يوضح كيفية استخدام أداة المراقبة لتحليل العمليات الفعلية على موقع الجزائر تيليكوم وبناء API client حقيقي يعمل بنفس الطريقة.

## المشكلة

الطريقة السابقة لم تعمل لأنها لم تكن مبنية على تحليل فعلي للموقع. الآن سنقوم بـ:

1. **مراقبة العمليات اليدوية** على الموقع الحقيقي
2. **تسجيل جميع طلبات HTTP** التي تحدث
3. **تحليل البيانات** المرسلة والمستقبلة
4. **بناء API client** مطابق للعمليات الحقيقية

## الأدوات المتوفرة

### 1. أداة المراقبة الأساسية
- **`api-monitor.js`** - مراقب API متقدم
- **`monitor-and-build.js`** - واجهة سهلة للاستخدام

### 2. Scripts سريعة
```bash
npm run monitor-api    # بدء المراقبة
npm run analyze-api    # تحليل البيانات
npm run monitor-help   # عرض التعليمات
```

## خطوات الاستخدام

### الخطوة 1: بدء المراقبة

```bash
# الطريقة السهلة
npm run monitor-api

# أو مباشرة
node monitor-and-build.js monitor
```

**ما سيحدث:**
1. ستظهر تعليمات مفصلة
2. ستختار نوع الخدمة (4G أو ADSL)
3. سيفتح المتصفح تلقائياً
4. ستبدأ المراقبة في الخلفية

### الخطوة 2: القيام بالعملية يدوياً

**في المتصفح المفتوح:**

1. **املأ رقم الهاتف** (استخدم رقم حقيقي)
2. **حل الكابتشا الأولى**
3. **اضغط زر التأكيد**
4. **املأ رقم البطاقة** (استخدم رقم حقيقي إن أمكن)
5. **حل الكابتشا الثانية**
6. **اضغط زر التأكيد النهائي**
7. **انتظر النتيجة** (نجح أو فشل)

**مهم:** لا تغلق المتصفح حتى تكتمل العملية!

### الخطوة 3: إنهاء المراقبة

في Terminal، اضغط **Ctrl+C** لإنهاء المراقبة وحفظ البيانات.

### الخطوة 4: تحليل البيانات

```bash
# تحليل البيانات المحفوظة
npm run analyze-api

# أو مباشرة
node monitor-and-build.js analyze
```

## الملفات المُنتجة

### 1. البيانات الخام
**`api_analysis.json`**
- جميع طلبات HTTP المسجلة
- Headers والبيانات المرسلة
- الاستجابات والأخطاء
- التوقيتات والتفاصيل

### 2. التقرير المفصل
**`api_detailed_log.txt`**
- ملخص الطلبات المهمة
- تحليل البيانات المرسلة
- اقتراحات لبناء API
- إحصائيات شاملة

### 3. API Client الجديد
**`real-api-client.js`**
- كود JavaScript جاهز للاستخدام
- مبني على البيانات الحقيقية
- يحاكي العمليات الفعلية

### 4. ملخص النتائج
**`API_ANALYSIS_RESULTS.md`**
- خلاصة التحليل
- تعليمات الاستخدام
- الخطوات التالية

## مثال على الاستخدام الكامل

```bash
# 1. بدء المراقبة
npm run monitor-api

# 2. اختيار الخدمة (في التطبيق)
# اختر: 1 (للـ 4G) أو 2 (للـ ADSL)

# 3. تأكيد البدء
# اكتب: نعم

# 4. القيام بالعملية في المتصفح
# (اتبع التعليمات المعروضة)

# 5. إنهاء المراقبة
# اضغط Ctrl+C

# 6. تحليل البيانات
npm run analyze-api

# 7. فحص النتائج
node monitor-and-build.js check
```

## نصائح مهمة

### للحصول على أفضل نتائج:

1. **استخدم بيانات حقيقية**
   - رقم هاتف صحيح
   - رقم بطاقة صحيح (إن أمكن)

2. **اتبع العملية كاملة**
   - لا تتخطى أي خطوة
   - انتظر تحميل الصفحات

3. **لا تغلق المتصفح**
   - حتى تكتمل العملية
   - حتى تضغط Ctrl+C

4. **تأكد من الاتصال**
   - إنترنت مستقر
   - موقع الجزائر تيليكوم متاح

### إذا فشلت العملية:

1. **راجع الملفات المُنتجة**
   - حتى لو فشلت، ستحصل على بيانات مفيدة

2. **كرر المحاولة**
   - جرب خدمة مختلفة (4G ↔ ADSL)
   - جرب في وقت مختلف

3. **تحقق من التفاصيل**
   - راجع `api_detailed_log.txt`
   - ابحث عن رسائل الخطأ

## تحليل النتائج

### فحص الطلبات المهمة

```bash
# عرض الطلبات POST
grep -A 5 "POST" api_detailed_log.txt

# عرض البيانات المرسلة
grep -A 3 "البيانات المرسلة" api_detailed_log.txt
```

### فهم البيانات

**ابحث عن:**
- طلبات POST للنماذج
- URLs الكابتشا
- البيانات المرسلة (form data)
- Cookies والجلسات
- رسائل الخطأ والنجاح

## بناء API Client محسن

بعد التحليل، يمكنك:

1. **استخدام API Client المُنتج**
   ```javascript
   const RealAPIClient = require('./real-api-client');
   const client = new RealAPIClient(captchaSolver);
   ```

2. **تطوير الكود حسب الحاجة**
   - إضافة معالجة أخطاء
   - تحسين الأداء
   - إضافة ميزات جديدة

3. **اختبار النتائج**
   - مقارنة مع الطريقة الأصلية
   - قياس الأداء
   - التأكد من الاستقرار

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. المتصفح لا يفتح
```bash
# تأكد من تثبيت Puppeteer
npm install puppeteer

# أو إعادة التثبيت
npm uninstall puppeteer
npm install puppeteer
```

#### 2. لا يتم تسجيل البيانات
- تأكد من إكمال العملية كاملة
- لا تغلق المتصفح مبكراً
- تأكد من الضغط على Ctrl+C

#### 3. ملفات فارغة أو ناقصة
- كرر العملية مع بيانات مختلفة
- تأكد من استقرار الاتصال
- جرب في وقت مختلف

#### 4. API Client لا يعمل
- راجع `api_detailed_log.txt`
- تحقق من URLs والبيانات
- قارن مع العملية اليدوية

## الخطوات التالية

بعد الحصول على API client حقيقي:

1. **دمجه في النظام الحالي**
2. **اختبار الأداء والاستقرار**
3. **مقارنة مع Puppeteer**
4. **تحسين وتطوير الكود**
5. **توثيق النتائج**

---

## خلاصة

هذه الأداة ستساعدك في:
- ✅ فهم كيفية عمل موقع الجزائر تيليكوم فعلياً
- ✅ الحصول على بيانات حقيقية للطلبات والاستجابات
- ✅ بناء API client يعمل بنفس طريقة الموقع
- ✅ تحسين الأداء والاستقرار

**ابدأ الآن:** `npm run monitor-api`
