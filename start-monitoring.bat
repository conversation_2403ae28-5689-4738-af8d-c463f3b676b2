@echo off
chcp 65001 >nul
title مراقب API - الجزائر تيليكوم

echo.
echo ===============================================================================
echo                    🔍 مراقب API - الجزائر تيليكوم
echo ===============================================================================
echo.
echo 📋 هذه الأداة ستقوم بـ:
echo    1. فتح المتصفح على موقع الجزائر تيليكوم
echo    2. مراقبة جميع طلبات HTTP التي تحدث
echo    3. تسجيل البيانات المرسلة والمستقبلة
echo    4. بناء API client حقيقي بناءً على التحليل
echo.
echo 🎯 ما عليك فعله:
echo    1. انتظر حتى يفتح المتصفح
echo    2. قم بعملية التعبئة يدوياً كما تفعل عادة
echo    3. املأ رقم الهاتف وحل الكابتشا
echo    4. املأ رقم البطاقة وحل الكابتشا الثانية
echo    5. انتظر النتيجة
echo    6. اضغط Ctrl+C هنا لإنهاء المراقبة
echo.
echo ⚠️ مهم: استخدم بيانات حقيقية للحصول على أفضل نتائج
echo.

:menu
echo ===============================================================================
echo 🌐 اختر نوع الخدمة:
echo    1. 4G LTE
echo    2. ADSL
echo    3. عرض التعليمات
echo    4. فحص الملفات الموجودة
echo    5. تحليل البيانات المحفوظة
echo    0. خروج
echo ===============================================================================
echo.

set /p choice="أدخل رقم الخيار: "

if "%choice%"=="1" (
    echo.
    echo 🚀 بدء مراقبة خدمة 4G...
    echo 📱 سيتم فتح المتصفح خلال ثوانٍ...
    echo.
    node api-monitor.js monitor 4g
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 🚀 بدء مراقبة خدمة ADSL...
    echo 📱 سيتم فتح المتصفح خلال ثوانٍ...
    echo.
    node api-monitor.js monitor adsl
    goto end
)

if "%choice%"=="3" (
    echo.
    echo ===============================================================================
    echo                              📋 التعليمات
    echo ===============================================================================
    echo.
    echo 🎯 الهدف:
    echo    هذه الأداة تقوم بمراقبة العمليات اليدوية على موقع الجزائر تيليكوم
    echo    وتسجيل جميع طلبات HTTP لبناء API client حقيقي يعمل بنفس الطريقة
    echo.
    echo 📋 الخطوات:
    echo    1. اختر نوع الخدمة (4G أو ADSL)
    echo    2. انتظر فتح المتصفح
    echo    3. قم بالعملية يدوياً في المتصفح
    echo    4. اضغط Ctrl+C هنا لإنهاء المراقبة
    echo    5. اختر "تحليل البيانات" من القائمة
    echo.
    echo 📁 الملفات المُنتجة:
    echo    - api_analysis.json      : البيانات الخام
    echo    - api_detailed_log.txt   : تقرير مفصل
    echo    - real-api-client.js     : API client جديد
    echo    - API_ANALYSIS_RESULTS.md : ملخص النتائج
    echo.
    pause
    goto menu
)

if "%choice%"=="4" (
    echo.
    echo 📁 فحص الملفات الموجودة...
    echo.
    node monitor-and-build.js check
    echo.
    pause
    goto menu
)

if "%choice%"=="5" (
    echo.
    echo 🔍 تحليل البيانات المحفوظة...
    echo.
    node api-monitor.js analyze
    echo.
    echo ✅ تم التحليل! راجع الملفات المُنشأة:
    echo    - real-api-client.js
    echo    - API_ANALYSIS_RESULTS.md
    echo.
    pause
    goto menu
)

if "%choice%"=="0" (
    goto end
)

echo.
echo ❌ خيار غير صحيح. يرجى المحاولة مرة أخرى.
echo.
pause
goto menu

:end
echo.
echo 👋 شكراً لاستخدام مراقب API!
echo.
pause
