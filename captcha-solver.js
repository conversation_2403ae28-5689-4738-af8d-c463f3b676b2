const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class AZCaptchaSolver {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://azcaptcha.com';
    }

    /**
     * حل الكابتشا باستخدام خدمة AZCaptcha
     * @param {string} imagePath - مسار صورة الكابتشا
     * @returns {Promise<string>} - النص المحلول للكابتشا
     */
    async solveCaptcha(imagePath) {
        try {
            console.log('🔍 بدء حل الكابتشا باستخدام AZCaptcha...');

            // التحقق من صحة الصورة قبل الرفع
            await this.validateCaptchaImage(imagePath);

            // الخطوة 1: رفع صورة الكابتشا
            const taskId = await this.submitCaptcha(imagePath);
            console.log(`📤 تم رفع الكابتشا، معرف المهمة: ${taskId}`);

            // الخطوة 2: انتظار الحل
            const solution = await this.getCaptchaSolution(taskId);
            console.log(`✅ تم حل الكابتشا: ${solution}`);

            return solution;
        } catch (error) {
            console.error('❌ خطأ في حل الكابتشا:', error.message);
            throw error;
        }
    }

    /**
     * التحقق من صحة صورة الكابتشا
     * @param {string} imagePath - مسار صورة الكابتشا
     */
    async validateCaptchaImage(imagePath) {
        if (!fs.existsSync(imagePath)) {
            throw new Error('ملف صورة الكابتشا غير موجود');
        }

        const stats = fs.statSync(imagePath);
        console.log(`📊 حجم صورة الكابتشا: ${stats.size} بايت`);

        if (stats.size < 500) {
            throw new Error('حجم صورة الكابتشا صغير جداً - قد تكون فارغة أو تالفة');
        }

        if (stats.size > 1024 * 1024) { // 1MB
            throw new Error('حجم صورة الكابتشا كبير جداً');
        }

        console.log('✅ تم التحقق من صحة صورة الكابتشا');
    }

    /**
     * رفع صورة الكابتشا إلى AZCaptcha
     * @param {string} imagePath - مسار صورة الكابتشا
     * @returns {Promise<string>} - معرف المهمة
     */
    async submitCaptcha(imagePath) {
        const formData = new FormData();
        formData.append('method', 'post');
        formData.append('key', this.apiKey);
        formData.append('file', fs.createReadStream(imagePath));
        formData.append('json', '1');

        const response = await axios.post(`${this.baseUrl}/in.php`, formData, {
            headers: {
                ...formData.getHeaders(),
            },
            timeout: 30000
        });

        if (response.data.status !== 1) {
            throw new Error(`فشل في رفع الكابتشا: ${response.data.error_text || 'خطأ غير معروف'}`);
        }

        return response.data.request;
    }

    /**
     * الحصول على حل الكابتشا
     * @param {string} taskId - معرف المهمة
     * @returns {Promise<string>} - النص المحلول
     */
    async getCaptchaSolution(taskId) {
        const maxAttempts = 30; // 30 محاولة (5 دقائق)
        const delay = 10000; // 10 ثواني بين كل محاولة

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                console.log(`🔄 محاولة ${attempt}/${maxAttempts} للحصول على الحل...`);
                
                const response = await axios.get(`${this.baseUrl}/res.php`, {
                    params: {
                        key: this.apiKey,
                        action: 'get',
                        id: taskId,
                        json: 1
                    },
                    timeout: 15000
                });

                if (response.data.status === 1) {
                    return response.data.request;
                } else if (response.data.error_text === 'CAPCHA_NOT_READY') {
                    console.log('⏳ الكابتشا لم تُحل بعد، انتظار...');
                    await this.sleep(delay);
                    continue;
                } else {
                    throw new Error(`خطأ في الحصول على الحل: ${response.data.error_text}`);
                }
            } catch (error) {
                if (attempt === maxAttempts) {
                    throw new Error(`انتهت المحاولات: ${error.message}`);
                }
                console.log(`⚠️ خطأ في المحاولة ${attempt}: ${error.message}`);
                await this.sleep(delay);
            }
        }

        throw new Error('انتهت المهلة الزمنية لحل الكابتشا');
    }

    /**
     * تنزيل صورة الكابتشا من الصفحة باستخدام screenshot
     * @param {Object} page - صفحة Puppeteer
     * @param {string} captchaSelector - محدد صورة الكابتشا
     * @returns {Promise<string>} - مسار الصورة المحفوظة
     */
    async downloadCaptchaImage(page, captchaSelector) {
        try {
            console.log('📥 تنزيل صورة الكابتشا...');

            // انتظار ظهور صورة الكابتشا
            await page.waitForSelector(captchaSelector, { timeout: 10000 });

            // التأكد من تحميل الصورة بالكامل
            await page.waitForFunction((selector) => {
                const img = document.querySelector(selector);
                return img && img.complete && img.naturalHeight !== 0;
            }, { timeout: 10000 }, captchaSelector);

            console.log('✅ تم تحميل صورة الكابتشا بالكامل');

            // أخذ screenshot للعنصر مباشرة
            const timestamp = Date.now();
            const imagePath = `./captcha_${timestamp}.png`;

            const captchaElement = await page.$(captchaSelector);
            if (!captchaElement) {
                throw new Error('لم يتم العثور على عنصر الكابتشا');
            }

            // أخذ screenshot للعنصر فقط
            await captchaElement.screenshot({
                path: imagePath,
                type: 'png'
            });

            console.log(`💾 تم حفظ صورة الكابتشا: ${imagePath}`);

            // التحقق من وجود الملف وحجمه
            if (fs.existsSync(imagePath)) {
                const stats = fs.statSync(imagePath);
                if (stats.size < 100) {
                    throw new Error('حجم صورة الكابتشا صغير جداً - قد تكون فارغة');
                }
                console.log(`📊 حجم صورة الكابتشا: ${stats.size} بايت`);
            }

            return imagePath;

        } catch (error) {
            console.error('❌ خطأ في تنزيل صورة الكابتشا:', error.message);

            // محاولة بديلة: تنزيل عبر الرابط مع الكوكيز
            try {
                console.log('🔄 محاولة بديلة: تنزيل عبر الرابط...');
                return await this.downloadCaptchaViaURL(page, captchaSelector);
            } catch (fallbackError) {
                console.error('❌ فشلت المحاولة البديلة:', fallbackError.message);
                throw error;
            }
        }
    }

    /**
     * تنزيل صورة الكابتشا عبر الرابط مع الكوكيز
     * @param {Object} page - صفحة Puppeteer
     * @param {string} captchaSelector - محدد صورة الكابتشا
     * @returns {Promise<string>} - مسار الصورة المحفوظة
     */
    async downloadCaptchaViaURL(page, captchaSelector) {
        // الحصول على رابط الصورة
        const imageData = await page.evaluate((selector) => {
            const img = document.querySelector(selector);
            if (!img) return null;

            return {
                src: img.src,
                baseURI: img.baseURI
            };
        }, captchaSelector);

        if (!imageData || !imageData.src) {
            throw new Error('لم يتم العثور على رابط صورة الكابتشا');
        }

        // الحصول على الكوكيز من الصفحة
        const cookies = await page.cookies();
        const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

        // تنزيل الصورة مع الكوكيز
        const timestamp = Date.now();
        const imagePath = `./captcha_${timestamp}.png`;

        const response = await axios.get(imageData.src, {
            responseType: 'stream',
            timeout: 15000,
            headers: {
                'Cookie': cookieString,
                'Referer': imageData.baseURI,
                'User-Agent': await page.evaluate(() => navigator.userAgent)
            }
        });

        const writer = fs.createWriteStream(imagePath);
        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', () => {
                console.log(`💾 تم حفظ صورة الكابتشا (عبر الرابط): ${imagePath}`);
                resolve(imagePath);
            });
            writer.on('error', reject);
        });
    }

    /**
     * تنظيف الملفات المؤقتة
     * @param {string} imagePath - مسار الصورة
     */
    async cleanupImage(imagePath) {
        try {
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
                console.log(`🗑️ تم حذف الصورة المؤقتة: ${imagePath}`);
            }
        } catch (error) {
            console.warn('⚠️ تعذر حذف الصورة المؤقتة:', error.message);
        }
    }

    /**
     * انتظار لفترة محددة
     * @param {number} ms - المدة بالميلي ثانية
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * التحقق من صحة مفتاح API
     * @returns {Promise<boolean>} - true إذا كان المفتاح صحيح
     */
    async validateApiKey() {
        try {
            const response = await axios.get(`${this.baseUrl}/res.php`, {
                params: {
                    key: this.apiKey,
                    action: 'getbalance',
                    json: 1
                },
                timeout: 10000
            });

            if (response.data.status === 1) {
                console.log(`💰 رصيد AZCaptcha: $${response.data.request}`);
                return true;
            } else {
                console.error('❌ مفتاح API غير صحيح:', response.data.error_text);
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في التحقق من مفتاح API:', error.message);
            return false;
        }
    }
}

module.exports = AZCaptchaSolver;
