const axios = require('axios');

/**
 * مثال شامل لاستخدام API Client الجديد
 * هذا المثال يوضح كيفية استخدام الطريقة الجديدة بدلاً من Puppeteer
 */

const BASE_URL = 'http://localhost:3000';

// مثال 1: تعبئة بطاقة 4G باستخدام API Client
async function example1_recharge4G() {
    console.log('🔸 مثال 1: تعبئة بطاقة 4G باستخدام API Client');
    
    try {
        const requestData = {
            serviceType: '4g',
            phoneNumber: '0555123456',  // ضع رقم هاتف حقيقي هنا
            voucherCode: '1234567890123456'  // ضع رقم بطاقة حقيقي هنا
        };

        console.log('📤 إرسال طلب التعبئة...');
        console.log('📱 رقم الهاتف:', requestData.phoneNumber);
        console.log('💳 رقم البطاقة:', requestData.voucherCode.substring(0, 4) + '****' + requestData.voucherCode.substring(requestData.voucherCode.length - 4));

        const response = await axios.post(`${BASE_URL}/recharge-api`, requestData, {
            timeout: 120000, // مهلة زمنية 2 دقيقة
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('\n📊 نتيجة العملية:');
        console.log('✅ حالة النجاح:', response.data.success ? 'نجح' : 'فشل');
        console.log('📝 الرسالة:', response.data.message);
        console.log('🔧 الطريقة المستخدمة:', response.data.method);
        console.log('⏰ وقت العملية:', response.data.timestamp);

        if (response.data.extractedInfo) {
            console.log('\n📋 المعلومات المستخرجة:');
            console.log('🆔 رقم العملية:', response.data.extractedInfo.operationNumber || 'غير متوفر');
            console.log('📞 رقم الهاتف المؤكد:', response.data.extractedInfo.phoneNumber || 'غير متوفر');
            console.log('👤 رقم العميل:', response.data.extractedInfo.clientNumber || 'غير متوفر');
            console.log('📅 التاريخ:', response.data.extractedInfo.date || 'غير متوفر');
            console.log('🕐 الوقت:', response.data.extractedInfo.time || 'غير متوفر');
        }

        if (response.data.pageContent) {
            console.log('\n📄 محتوى الصفحة متوفر للمراجعة');
            // يمكنك حفظ محتوى الصفحة في ملف للمراجعة
            // fs.writeFileSync('page_content.html', response.data.pageContent);
        }

        return response.data;

    } catch (error) {
        console.error('❌ خطأ في العملية:', error.message);
        
        if (error.response) {
            console.error('📊 تفاصيل الخطأ:', error.response.data);
        }
        
        throw error;
    }
}

// مثال 2: تعبئة بطاقة ADSL باستخدام API Client
async function example2_rechargeADSL() {
    console.log('\n🔸 مثال 2: تعبئة بطاقة ADSL باستخدام API Client');
    
    try {
        const requestData = {
            serviceType: 'adsl',
            phoneNumber: '029123456',  // ضع رقم هاتف ADSL حقيقي هنا
            voucherCode: '9876543210987654'  // ضع رقم بطاقة حقيقي هنا
        };

        console.log('📤 إرسال طلب التعبئة...');
        console.log('📱 رقم الهاتف:', requestData.phoneNumber);
        console.log('💳 رقم البطاقة:', requestData.voucherCode.substring(0, 4) + '****' + requestData.voucherCode.substring(requestData.voucherCode.length - 4));

        const response = await axios.post(`${BASE_URL}/recharge-api`, requestData, {
            timeout: 120000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('\n📊 نتيجة العملية:');
        console.log('✅ حالة النجاح:', response.data.success ? 'نجح' : 'فشل');
        console.log('📝 الرسالة:', response.data.message);
        console.log('🔧 الطريقة المستخدمة:', response.data.method);

        return response.data;

    } catch (error) {
        console.error('❌ خطأ في العملية:', error.message);
        throw error;
    }
}

// مثال 3: مقارنة بين الطريقتين
async function example3_compareMethodsDemo() {
    console.log('\n🔸 مثال 3: مقارنة نظرية بين الطريقتين');
    
    console.log('\n📊 مقارنة الطرق:');
    console.log('┌─────────────────────┬──────────────────────┬──────────────────────┐');
    console.log('│ الخاصية             │ Puppeteer (/recharge)│ API Client (/recharge-api)│');
    console.log('├─────────────────────┼──────────────────────┼──────────────────────┤');
    console.log('│ السرعة              │ بطيء (30-60 ثانية)   │ سريع (10-20 ثانية)   │');
    console.log('│ استهلاك الموارد     │ عالي                 │ منخفض                │');
    console.log('│ الاستقرار          │ متوسط                │ عالي                 │');
    console.log('│ سهولة الصيانة      │ معقد                 │ بسيط                 │');
    console.log('│ قابلية الكشف       │ متوسطة               │ منخفضة               │');
    console.log('│ دعم الكابتشا       │ ✅                   │ ✅                   │');
    console.log('│ استخراج المعلومات  │ ✅                   │ ✅                   │');
    console.log('│ حفظ قاعدة البيانات │ ✅                   │ ✅                   │');
    console.log('└─────────────────────┴──────────────────────┴──────────────────────┘');
    
    console.log('\n💡 متى تستخدم كل طريقة:');
    console.log('🌐 Puppeteer (/recharge):');
    console.log('   - عندما تحتاج لمحاكاة سلوك المستخدم بدقة');
    console.log('   - عندما يكون الموقع معقد جداً');
    console.log('   - للتطوير والاختبار');
    
    console.log('\n⚡ API Client (/recharge-api):');
    console.log('   - للاستخدام في الإنتاج');
    console.log('   - عندما تحتاج لأداء عالي');
    console.log('   - عندما تريد توفير الموارد');
    console.log('   - للتطبيقات التي تحتاج استجابة سريعة');
}

// مثال 4: معالجة الأخطاء المختلفة
async function example4_errorHandling() {
    console.log('\n🔸 مثال 4: معالجة الأخطاء المختلفة');
    
    const testCases = [
        {
            name: 'بيانات ناقصة',
            data: { phoneNumber: '0123456789' }, // نقص voucherCode
            expectedError: 'رقم الهاتف ورقم البطاقة مطلوبان'
        },
        {
            name: 'نوع خدمة خاطئ',
            data: { 
                phoneNumber: '0123456789', 
                voucherCode: '1234567890123456',
                serviceType: 'invalid'
            },
            expectedError: 'نوع الخدمة يجب أن يكون 4g أو adsl'
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n🧪 اختبار: ${testCase.name}`);
        
        try {
            const response = await axios.post(`${BASE_URL}/recharge-api`, testCase.data);
            console.log('⚠️ لم يحدث خطأ كما متوقع');
        } catch (error) {
            if (error.response && error.response.status === 400) {
                console.log('✅ تم التعامل مع الخطأ بشكل صحيح');
                console.log('📝 رسالة الخطأ:', error.response.data.message);
            } else {
                console.log('❌ خطأ غير متوقع:', error.message);
            }
        }
    }
}

// مثال 5: مراقبة العمليات
async function example5_monitorOperations() {
    console.log('\n🔸 مثال 5: مراقبة العمليات');
    
    try {
        // الحصول على آخر العمليات
        const operationsResponse = await axios.get(`${BASE_URL}/api/operations?limit=5`);
        
        console.log('\n📊 آخر 5 عمليات:');
        operationsResponse.data.data.forEach((operation, index) => {
            console.log(`${index + 1}. ${operation.service_type.toUpperCase()} - ${operation.success ? '✅ نجح' : '❌ فشل'} - ${operation.created_at}`);
        });

        // الحصول على الإحصائيات العامة
        const statsResponse = await axios.get(`${BASE_URL}/api/stats/overall`);
        
        console.log('\n📈 الإحصائيات العامة:');
        console.log('📊 إجمالي العمليات:', statsResponse.data.data.total_operations);
        console.log('✅ العمليات الناجحة:', statsResponse.data.data.successful_operations);
        console.log('❌ العمليات الفاشلة:', statsResponse.data.data.failed_operations);
        console.log('📊 معدل النجاح:', statsResponse.data.data.success_rate + '%');

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error.message);
    }
}

// تشغيل جميع الأمثلة
async function runAllExamples() {
    console.log('🚀 بدء تشغيل جميع الأمثلة...\n');
    
    try {
        // التحقق من حالة الخادم أولاً
        await axios.get(`${BASE_URL}/health`);
        console.log('✅ الخادم يعمل بشكل طبيعي\n');
        
        // تشغيل الأمثلة النظرية (بدون عمليات حقيقية)
        await example3_compareMethodsDemo();
        await example4_errorHandling();
        await example5_monitorOperations();
        
        console.log('\n⚠️ الأمثلة 1 و 2 معطلة لتجنب استهلاك الرصيد');
        console.log('💡 لتشغيلها، قم بإلغاء التعليق عن الأسطر في نهاية الملف');
        
        // لتشغيل العمليات الحقيقية، قم بإلغاء التعليق عن الأسطر التالية:
        // await example1_recharge4G();
        // await example2_rechargeADSL();
        
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.error('❌ الخادم غير متاح. تأكد من تشغيله: npm start');
        } else {
            console.error('❌ خطأ:', error.message);
        }
    }
}

// تشغيل الأمثلة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    runAllExamples();
}

module.exports = {
    example1_recharge4G,
    example2_rechargeADSL,
    example3_compareMethodsDemo,
    example4_errorHandling,
    example5_monitorOperations,
    runAllExamples
};
