# نظام التعبئة الآلي - الجزائر تيليكوم

نظام آلي لتعبئة بطاقات الإنترنت من الجزائر تيليكوم باستخدام Node.js و Express و Puppeteer.

## المميزات

- ✅ تعبئة آلية لبطاقات 4G LTE
- ✅ تعبئة آلية لبطاقات ADSL
- ✅ حل الكابتشا تلقائياً باستخدام AZCaptcha
- ✅ واجهة ويب سهلة الاستخدام مع اختيار نوع الخدمة
- ✅ دعم اللغة العربية
- ✅ معالجة الأخطاء والاستثناءات
- ✅ تسجيل العمليات (Logging)
- ✅ البحث الذكي عن عناصر النموذج
- ✅ **إرجاع محتوى الصفحة النهائية** مع المعلومات المستخرجة
- ✅ **استخراج تلقائي** لرقم العملية وتفاصيل العملية
- ✅ **قاعدة بيانات شاملة** لتسجيل جميع العمليات
- ✅ **لوحة مراقبة تفاعلية** لمتابعة الأداء والنتائج
- 🆕 **طريقة API Client جديدة** - أسرع وأكثر استقراراً
- 🆕 **اختيار الطريقة** - Puppeteer أو API Client
- 🆕 **أداء محسن** - استهلاك أقل للموارد بـ 80%

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- متصفح Chrome/Chromium

## التثبيت

1. تثبيت المتطلبات:
```bash
npm install
```

2. إعداد مفتاح AZCaptcha:
   - احصل على مفتاح API من https://azcaptcha.com
   - حدث ملف `.env`:
   ```env
   AZCAPTCHA_API_KEY=your_actual_api_key_here
   ```

3. تشغيل الخادم:
```bash
npm start
```

أو للتطوير مع إعادة التشغيل التلقائي:
```bash
npm run dev
```

4. فتح المتصفح والذهاب إلى:
```
http://localhost:3000
```

## الاستخدام

### عبر واجهة الويب:
1. افتح http://localhost:3000
2. اختر نوع الخدمة (4G أو ADSL)
3. أدخل رقم الهاتف
4. أدخل رقم البطاقة
5. اضغط على "بدء التعبئة الآلية"

### عبر API:

#### الطريقة الجديدة (API Client) - مُوصى بها:

**لتعبئة 4G:**
```bash
curl -X POST http://localhost:3000/recharge-api \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "4g",
    "phoneNumber": "0123456789",
    "voucherCode": "1234567890123456"
  }'
```

**لتعبئة ADSL:**
```bash
curl -X POST http://localhost:3000/recharge-api \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "adsl",
    "phoneNumber": "0123456789",
    "voucherCode": "1234567890123456"
  }'
```

#### الطريقة الأصلية (Puppeteer):

**لتعبئة 4G:**
```bash
curl -X POST http://localhost:3000/recharge \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "4g",
    "phoneNumber": "0123456789",
    "voucherCode": "1234567890123456"
  }'
```

**لتعبئة ADSL:**
```bash
curl -X POST http://localhost:3000/recharge \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "adsl",
    "phoneNumber": "0123456789",
    "voucherCode": "1234567890123456"
  }'
```

## 🆕 الطريقة الجديدة - API Client

### مقارنة الطرق

| المعيار | Puppeteer | API Client |
|---------|-----------|------------|
| **السرعة** | 30-60 ثانية | 10-20 ثانية |
| **استهلاك الذاكرة** | 100-200 MB | 10-20 MB |
| **استهلاك المعالج** | عالي | منخفض |
| **الاستقرار** | متوسط | عالي |
| **قابلية الكشف** | متوسطة | منخفضة |
| **سهولة الصيانة** | معقدة | بسيطة |

### متى تستخدم كل طريقة؟

#### 🌐 Puppeteer (`/recharge`)
- للتطوير والاختبار
- عندما تحتاج لمحاكاة سلوك المستخدم بدقة
- عندما يكون الموقع معقد جداً

#### ⚡ API Client (`/recharge-api`) - مُوصى به
- للاستخدام في الإنتاج
- عندما تحتاج لأداء عالي
- عندما تريد توفير الموارد
- للتطبيقات التي تحتاج استجابة سريعة

## الميزات الجديدة

### 📄 محتوى الصفحة النهائية
النظام الآن يعيد محتوى الصفحة النهائية كاملاً مع:
- **رقم العملية** المستخرج تلقائياً
- **تفاصيل العملية** (التاريخ، الوقت، رقم العميل)
- **محتوى الصفحة الكامل** للمراجعة والتوثيق

### 🔍 استخراج المعلومات التلقائي
```json
{
  "extractedInfo": {
    "operationNumber": "v50075495476",
    "phoneNumber": "029620296",
    "clientNumber": "10000025678064",
    "date": "2025-09-02",
    "time": "08:55"
  }
}
```

### 📊 نظام المراقبة والتسجيل
- **قاعدة بيانات SQLite** لحفظ جميع العمليات
- **لوحة مراقبة تفاعلية** على `http://localhost:3000/dashboard`
- **إحصائيات شاملة** ومعدلات النجاح
- **فلترة متقدمة** حسب التاريخ ونوع الخدمة والحالة
- **تفاصيل كاملة** لكل عملية مع محتوى الصفحة

## الخدمات المدعومة

النظام يدعم نوعين من خدمات تعبئة الإنترنت:

### 1. خدمة 4G LTE
- **الرابط**: `https://paiement.at.dz/index.php?p=voucher_internet&produit=4g`
- **الوصف**: تعبئة بطاقات الإنترنت للهواتف المحمولة
- **نوع الخدمة**: `4g`

### 2. خدمة ADSL
- **الرابط**: `https://paiement.at.dz/index.php?p=voucher_internet&produit=in`
- **الوصف**: تعبئة بطاقات الإنترنت للخطوط الثابتة
- **نوع الخدمة**: `adsl`

## ملاحظات مهمة

⚠️ **تحذير**: هذا النظام للأغراض التعليمية والاختبار فقط. تأكد من:

1. **الكابتشا**: النظام يتطلب تدخل يدوي لحل الكابتشا
2. **معدل الطلبات**: لا تفرط في استخدام النظام لتجنب الحظر
3. **الأمان**: لا تشارك أرقام البطاقات مع أطراف ثالثة
4. **القوانين**: تأكد من الامتثال لشروط استخدام الجزائر تيليكوم

## البنية التقنية

- **Backend**: Node.js + Express
- **Automation**: Puppeteer
- **Frontend**: HTML + CSS + JavaScript
- **Styling**: CSS3 مع تدرجات وانيميشن

## الملفات الرئيسية

### الملفات الأساسية
- `server.js` - الخادم الرئيسي
- `captcha-solver.js` - فئة حل الكابتشا
- `database.js` - نظام قاعدة البيانات
- `public/index.html` - واجهة المستخدم
- `package.json` - إعدادات المشروع
- `.env` - متغيرات البيئة

### الملفات الجديدة (API Client)
- `api-client.js` - فئة API Client الجديدة
- `test-api-client.js` - اختبار الطريقة الجديدة
- `example-api-client.js` - أمثلة الطريقة الجديدة
- `API_CLIENT_GUIDE.md` - دليل شامل للطريقة الجديدة

### ملفات الاختبار والأمثلة
- `example-usage.js` - أمثلة الطريقة الأصلية
- `test-api.js` - اختبار الطريقة الأصلية
- `test-database.js` - اختبار قاعدة البيانات

## ملفات التوثيق

- `DUAL_SERVICE_SUPPORT.md` - دليل دعم الخدمات المتعددة
- `PAGE_CONTENT_FEATURE.md` - دليل ميزة محتوى الصفحة
- `DATABASE_MONITORING_SYSTEM.md` - دليل نظام قاعدة البيانات والمراقبة
- `.env.example` - مثال على ملف المتغيرات

## الاختبار والأمثلة

### اختبار النظام

#### اختبار الطريقة الأصلية (Puppeteer)
```bash
node test-api.js
```

#### اختبار الطريقة الجديدة (API Client)
```bash
npm run test-api-client
```

سيقوم الاختبار بفحص:
- حالة الخادم
- مفتاح AZCaptcha
- إعداد بيانات الاختبار لكلا النوعين
- مقارنة الأداء بين الطرق

### أمثلة الاستخدام

#### أمثلة الطريقة الأصلية (Puppeteer)
```bash
node example-usage.js
```

#### أمثلة الطريقة الجديدة (API Client)
```bash
npm run example-api-client
```

يحتوي على أمثلة عملية لـ:
- تعبئة 4G و ADSL
- معالجة الاستجابة الكاملة
- استخراج المعلومات المهمة
- حفظ محتوى الصفحة
- مقارنة الأداء بين الطرق
- معالجة الأخطاء المختلفة

### اختبار قاعدة البيانات
```bash
node test-database.js
```

يختبر:
- APIs قاعدة البيانات
- الإحصائيات والتقارير
- الفلترة والبحث
- لوحة المراقبة

## لوحة المراقبة

### الوصول
افتح المتصفح واذهب إلى: `http://localhost:3000/dashboard`

### المميزات
- **📊 إحصائيات فورية**: إجمالي العمليات، معدل النجاح، توزيع الخدمات
- **🔍 فلترة متقدمة**: حسب نوع الخدمة، الحالة، التاريخ، رقم الهاتف
- **📋 جدول تفاعلي**: عرض العمليات مع إمكانية النقر للتفاصيل
- **🔄 تحديث تلقائي**: كل 30 ثانية
- **📱 تصميم متجاوب**: يعمل على جميع الأجهزة

### البيانات المعروضة
- رقم العملية من الموقع
- نوع الخدمة (4G/ADSL)
- رقم الهاتف
- حالة العملية (ناجحة/فاشلة)
- التاريخ والوقت
- تفاصيل كاملة عند النقر

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تشغيل Puppeteer**:
   - تأكد من تثبيت Chrome/Chromium
   - جرب تشغيل النظام كمدير

2. **مهلة انتهاء الوقت**:
   - تحقق من اتصال الإنترنت
   - تأكد من أن موقع الجزائر تيليكوم متاح

3. **فشل في العثور على العناصر**:
   - قد يكون الموقع قد غير بنيته
   - تحقق من selectors في الكود

## التطوير

لتطوير النظام أكثر:

1. تحسين معالجة الكابتشا (OCR)
2. إضافة قاعدة بيانات لتسجيل العمليات
3. إضافة نظام مصادقة
4. تحسين معالجة الأخطاء

## الترخيص

هذا المشروع للأغراض التعليمية فقط. استخدمه بمسؤولية.
