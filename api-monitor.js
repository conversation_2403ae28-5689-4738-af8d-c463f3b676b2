const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class APIMonitor {
    constructor() {
        this.requests = [];
        this.responses = [];
        this.cookies = [];
        this.headers = [];
        this.formData = [];
        this.logFile = path.join(__dirname, 'api_analysis.json');
        this.detailedLogFile = path.join(__dirname, 'api_detailed_log.txt');
    }

    /**
     * بدء مراقبة العمليات اليدوية على موقع الجزائر تيليكوم
     */
    async startMonitoring(serviceType = '4g') {
        console.log('🔍 بدء مراقبة API calls للموقع...');
        console.log('📋 سيتم فتح المتصفح - قم بالعملية يدوياً وسيتم تسجيل كل شيء');
        
        const browser = await puppeteer.launch({
            headless: false,
            devtools: true, // فتح أدوات المطور
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        const page = await browser.newPage();
        
        // تفعيل مراقبة الشبكة
        await page.setRequestInterception(true);
        
        // مراقبة جميع الطلبات
        page.on('request', (request) => {
            this.logRequest(request);
        });

        // مراقبة جميع الاستجابات
        page.on('response', async (response) => {
            await this.logResponse(response);
        });

        // تحديد الرابط حسب نوع الخدمة
        const serviceUrls = {
            '4g': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=4g',
            'adsl': 'https://paiement.at.dz/index.php?p=voucher_internet&produit=in'
        };

        const targetUrl = serviceUrls[serviceType];
        console.log(`🌐 الانتقال إلى: ${targetUrl}`);
        
        await page.goto(targetUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        console.log('\n📋 تعليمات:');
        console.log('1. قم بتعبئة رقم الهاتف');
        console.log('2. حل الكابتشا الأولى');
        console.log('3. اضغط على زر التأكيد');
        console.log('4. قم بتعبئة رقم البطاقة');
        console.log('5. حل الكابتشا الثانية');
        console.log('6. اضغط على زر التأكيد النهائي');
        console.log('7. انتظر حتى تظهر النتيجة');
        console.log('8. اضغط Ctrl+C في Terminal لإنهاء المراقبة');

        // انتظار إشارة الإنهاء
        process.on('SIGINT', async () => {
            console.log('\n🔄 جاري حفظ البيانات المسجلة...');
            await this.saveAnalysis();
            await browser.close();
            console.log('✅ تم حفظ التحليل بنجاح');
            process.exit(0);
        });

        // إبقاء المراقبة مفتوحة
        await new Promise(() => {}); // انتظار لا نهائي
    }

    /**
     * تسجيل الطلبات
     */
    logRequest(request) {
        const requestData = {
            timestamp: new Date().toISOString(),
            type: 'REQUEST',
            url: request.url(),
            method: request.method(),
            headers: request.headers(),
            postData: request.postData(),
            resourceType: request.resourceType()
        };

        this.requests.push(requestData);
        
        // طباعة الطلبات المهمة فقط
        if (this.isImportantRequest(request)) {
            console.log(`\n📤 طلب مهم: ${request.method()} ${request.url()}`);
            if (request.postData()) {
                console.log(`📋 البيانات: ${request.postData()}`);
            }
        }

        // السماح بالطلب
        request.continue();
    }

    /**
     * تسجيل الاستجابات
     */
    async logResponse(response) {
        const responseData = {
            timestamp: new Date().toISOString(),
            type: 'RESPONSE',
            url: response.url(),
            status: response.status(),
            statusText: response.statusText(),
            headers: response.headers(),
            contentType: response.headers()['content-type']
        };

        // محاولة الحصول على محتوى الاستجابة
        try {
            if (this.isImportantResponse(response)) {
                const text = await response.text();
                responseData.body = text;
                
                console.log(`\n📥 استجابة مهمة: ${response.status()} ${response.url()}`);
                console.log(`📋 نوع المحتوى: ${responseData.contentType}`);
                
                if (text.length < 1000) {
                    console.log(`📄 المحتوى: ${text.substring(0, 500)}...`);
                }
            }
        } catch (error) {
            responseData.bodyError = error.message;
        }

        this.responses.push(responseData);
    }

    /**
     * تحديد الطلبات المهمة
     */
    isImportantRequest(request) {
        const url = request.url().toLowerCase();
        const method = request.method();
        
        // طلبات POST مهمة
        if (method === 'POST') return true;
        
        // طلبات تحتوي على كلمات مفتاحية
        const keywords = [
            'voucher', 'recharge', 'captcha', 'securimage',
            'paiement', 'carte', 'validation', 'confirm'
        ];
        
        return keywords.some(keyword => url.includes(keyword));
    }

    /**
     * تحديد الاستجابات المهمة
     */
    isImportantResponse(response) {
        const url = response.url().toLowerCase();
        const status = response.status();
        
        // استجابات الأخطاء
        if (status >= 400) return true;
        
        // استجابات إعادة التوجيه
        if (status >= 300 && status < 400) return true;
        
        // استجابات تحتوي على كلمات مفتاحية
        const keywords = [
            'voucher', 'recharge', 'captcha', 'securimage',
            'paiement', 'carte', 'validation', 'confirm'
        ];
        
        return keywords.some(keyword => url.includes(keyword));
    }

    /**
     * حفظ التحليل في ملفات
     */
    async saveAnalysis() {
        const analysis = {
            timestamp: new Date().toISOString(),
            summary: {
                totalRequests: this.requests.length,
                totalResponses: this.responses.length,
                importantRequests: this.requests.filter(r => 
                    this.isImportantRequest({url: () => r.url, method: () => r.method})
                ).length
            },
            requests: this.requests,
            responses: this.responses
        };

        // حفظ التحليل الكامل في JSON
        fs.writeFileSync(this.logFile, JSON.stringify(analysis, null, 2));
        
        // حفظ ملخص مفصل في ملف نصي
        await this.generateDetailedReport();
        
        console.log(`📁 تم حفظ التحليل في: ${this.logFile}`);
        console.log(`📁 تم حفظ التقرير المفصل في: ${this.detailedLogFile}`);
    }

    /**
     * إنشاء تقرير مفصل
     */
    async generateDetailedReport() {
        let report = '='.repeat(80) + '\n';
        report += 'تقرير مفصل لمراقبة API - موقع الجزائر تيليكوم\n';
        report += '='.repeat(80) + '\n\n';
        
        report += `تاريخ التحليل: ${new Date().toLocaleString('ar-DZ')}\n`;
        report += `إجمالي الطلبات: ${this.requests.length}\n`;
        report += `إجمالي الاستجابات: ${this.responses.length}\n\n`;

        // تحليل الطلبات المهمة
        report += '📤 الطلبات المهمة:\n';
        report += '-'.repeat(50) + '\n';
        
        this.requests.forEach((req, index) => {
            if (this.isImportantRequest({url: () => req.url, method: () => req.method})) {
                report += `\n${index + 1}. ${req.method} ${req.url}\n`;
                report += `   الوقت: ${req.timestamp}\n`;
                
                if (req.postData) {
                    report += `   البيانات المرسلة: ${req.postData}\n`;
                }
                
                if (req.headers) {
                    report += `   Headers المهمة:\n`;
                    Object.entries(req.headers).forEach(([key, value]) => {
                        if (['content-type', 'referer', 'origin'].includes(key.toLowerCase())) {
                            report += `     ${key}: ${value}\n`;
                        }
                    });
                }
            }
        });

        // تحليل الاستجابات المهمة
        report += '\n\n📥 الاستجابات المهمة:\n';
        report += '-'.repeat(50) + '\n';
        
        this.responses.forEach((res, index) => {
            if (this.isImportantResponse({url: () => res.url, status: () => res.status})) {
                report += `\n${index + 1}. ${res.status} ${res.url}\n`;
                report += `   الوقت: ${res.timestamp}\n`;
                report += `   نوع المحتوى: ${res.contentType}\n`;
                
                if (res.body && res.body.length < 2000) {
                    report += `   المحتوى: ${res.body.substring(0, 500)}...\n`;
                }
            }
        });

        // اقتراحات لبناء API
        report += '\n\n🔧 اقتراحات لبناء API:\n';
        report += '-'.repeat(50) + '\n';
        
        const postRequests = this.requests.filter(r => r.method === 'POST');
        if (postRequests.length > 0) {
            report += '\nطلبات POST التي يجب محاكاتها:\n';
            postRequests.forEach((req, index) => {
                report += `${index + 1}. ${req.url}\n`;
                if (req.postData) {
                    report += `   البيانات: ${req.postData}\n`;
                }
            });
        }

        fs.writeFileSync(this.detailedLogFile, report);
    }

    /**
     * تحليل البيانات المحفوظة وإنشاء API client
     */
    async analyzeAndGenerateAPI() {
        if (!fs.existsSync(this.logFile)) {
            console.log('❌ لا يوجد ملف تحليل. قم بتشغيل المراقبة أولاً');
            return;
        }

        console.log('🔍 تحليل البيانات المحفوظة...');
        
        const data = JSON.parse(fs.readFileSync(this.logFile, 'utf8'));
        
        // استخراج الطلبات المهمة
        const importantRequests = data.requests.filter(r => 
            r.method === 'POST' || r.url.includes('captcha') || r.url.includes('voucher')
        );

        console.log(`📊 تم العثور على ${importantRequests.length} طلب مهم`);
        
        // إنشاء API client بناءً على التحليل
        await this.generateAPIClient(importantRequests, data.responses);
    }

    /**
     * إنشاء API client بناءً على التحليل
     */
    async generateAPIClient(requests, responses) {
        console.log('🔧 إنشاء API client جديد بناءً على التحليل...');
        
        let apiClientCode = `const axios = require('axios');
const cheerio = require('cheerio');
const FormData = require('form-data');

class AlgeriaTelecomRealAPIClient {
    constructor(captchaSolver) {
        this.captchaSolver = captchaSolver;
        this.session = axios.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
    }

    // تم إنشاء هذا الكود بناءً على تحليل API الفعلي
    // التاريخ: ${new Date().toISOString()}
    
`;

        // تحليل الطلبات وإنشاء الوظائف
        requests.forEach((req, index) => {
            if (req.method === 'POST') {
                apiClientCode += `
    // طلب ${index + 1}: ${req.url}
    async makeRequest${index + 1}(data) {
        try {
            const response = await this.session.post('${req.url}', data, {
                headers: ${JSON.stringify(req.headers, null, 16)}
            });
            return response.data;
        } catch (error) {
            throw new Error('خطأ في الطلب ${index + 1}: ' + error.message);
        }
    }
`;
            }
        });

        apiClientCode += `
}

module.exports = AlgeriaTelecomRealAPIClient;
`;

        // حفظ API client الجديد
        const apiClientFile = path.join(__dirname, 'real-api-client.js');
        fs.writeFileSync(apiClientFile, apiClientCode);
        
        console.log(`✅ تم إنشاء API client جديد: ${apiClientFile}`);
        
        // إنشاء ملف تعليمات
        const instructionsFile = path.join(__dirname, 'API_ANALYSIS_RESULTS.md');
        const instructions = `# نتائج تحليل API

## الملفات المُنشأة:
- \`${this.logFile}\` - البيانات الخام للتحليل
- \`${this.detailedLogFile}\` - تقرير مفصل
- \`${apiClientFile}\` - API client مبني على التحليل الفعلي

## الخطوات التالية:
1. راجع الملفات المُنشأة
2. اختبر API client الجديد
3. قم بتطوير الوظائف حسب الحاجة

## إجمالي الطلبات المحللة: ${requests.length}
## تاريخ التحليل: ${new Date().toLocaleString('ar-DZ')}
`;

        fs.writeFileSync(instructionsFile, instructions);
        console.log(`📋 تم إنشاء ملف التعليمات: ${instructionsFile}`);
    }
}

// تشغيل المراقبة
async function startMonitoring(serviceType = '4g') {
    const monitor = new APIMonitor();
    await monitor.startMonitoring(serviceType);
}

// تحليل البيانات المحفوظة
async function analyzeData() {
    const monitor = new APIMonitor();
    await monitor.analyzeAndGenerateAPI();
}

// إذا تم تشغيل الملف مباشرة
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0] || 'monitor';
    const serviceType = args[1] || '4g';
    
    if (command === 'monitor') {
        console.log(`🔍 بدء مراقبة خدمة ${serviceType.toUpperCase()}`);
        startMonitoring(serviceType);
    } else if (command === 'analyze') {
        console.log('🔍 تحليل البيانات المحفوظة');
        analyzeData();
    } else {
        console.log('الاستخدام:');
        console.log('node api-monitor.js monitor [4g|adsl]  # لبدء المراقبة');
        console.log('node api-monitor.js analyze           # لتحليل البيانات');
    }
}

module.exports = { APIMonitor, startMonitoring, analyzeData };
