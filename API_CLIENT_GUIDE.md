# دليل API Client الجديد - الجزائر تيليكوم

## نظرة عامة

تم إضافة طريقة جديدة لتعبئة بطاقات الجزائر تيليكوم باستخدام **API Client** بدلاً من Puppeteer. هذه الطريقة أسرع وأكثر استقراراً وتستهلك موارد أقل.

## المميزات الجديدة

### ⚡ الأداء المحسن
- **السرعة**: 3-5 مرات أسرع من Puppeteer
- **الذاكرة**: استهلاك أقل بـ 80-90%
- **المعالج**: استهلاك أقل بـ 70-80%
- **الاستقرار**: أقل عرضة للأخطاء والانقطاع

### 🔧 سهولة الاستخدام
- **API بسيط**: نفس واجهة API الأصلية
- **تبديل سهل**: يمكن التبديل بين الطرق بسهولة
- **نفس الميزات**: يدعم جميع الميزات الموجودة

## الاستخدام

### 1. عبر واجهة الويب

1. افتح http://localhost:3000
2. اختر **"API Client (سريع ومستقر)"** من قائمة طريقة التنفيذ
3. أدخل البيانات المطلوبة
4. اضغط "بدء التعبئة الآلية"

### 2. عبر API مباشرة

#### تعبئة 4G
```bash
curl -X POST http://localhost:3000/recharge-api \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "4g",
    "phoneNumber": "0123456789",
    "voucherCode": "1234567890123456"
  }'
```

#### تعبئة ADSL
```bash
curl -X POST http://localhost:3000/recharge-api \
  -H "Content-Type: application/json" \
  -d '{
    "serviceType": "adsl",
    "phoneNumber": "029123456",
    "voucherCode": "9876543210987654"
  }'
```

### 3. استخدام JavaScript

```javascript
const axios = require('axios');

async function rechargeWithAPIClient() {
    try {
        const response = await axios.post('http://localhost:3000/recharge-api', {
            serviceType: '4g',
            phoneNumber: '0123456789',
            voucherCode: '1234567890123456'
        });
        
        console.log('النتيجة:', response.data);
        
        if (response.data.success) {
            console.log('✅ تمت التعبئة بنجاح');
            if (response.data.extractedInfo) {
                console.log('رقم العملية:', response.data.extractedInfo.operationNumber);
            }
        } else {
            console.log('❌ فشلت التعبئة:', response.data.message);
        }
        
    } catch (error) {
        console.error('خطأ:', error.message);
    }
}
```

## مقارنة الطرق

| المعيار | Puppeteer | API Client |
|---------|-----------|------------|
| **السرعة** | 30-60 ثانية | 10-20 ثانية |
| **الذاكرة** | 100-200 MB | 10-20 MB |
| **المعالج** | عالي | منخفض |
| **الاستقرار** | متوسط | عالي |
| **قابلية الكشف** | متوسطة | منخفضة |
| **سهولة الصيانة** | معقدة | بسيطة |

## الاختبار

### اختبار النظام الجديد
```bash
node test-api-client.js
```

### تشغيل الأمثلة
```bash
node example-api-client.js
```

## استجابة API

### استجابة ناجحة
```json
{
  "success": true,
  "message": "تمت عملية التعبئة بنجاح ✅",
  "method": "API Client (بدون متصفح)",
  "serviceType": "4G",
  "phoneNumber": "0123456789",
  "voucherCode": "1234****3456",
  "errorType": null,
  "timestamp": "2025-09-03T10:30:00.000Z",
  "extractedInfo": {
    "operationNumber": "v50075495476",
    "phoneNumber": "0123456789",
    "clientNumber": "10000025678064",
    "date": "2025-09-03",
    "time": "10:30"
  },
  "pageContent": "محتوى الصفحة الكامل..."
}
```

### استجابة فاشلة
```json
{
  "success": false,
  "message": "خطأ في البطاقة: carte invalide",
  "method": "API Client (بدون متصفح)",
  "serviceType": "4G",
  "phoneNumber": "0123456789",
  "voucherCode": "1234****3456",
  "errorType": "voucher_error",
  "timestamp": "2025-09-03T10:30:00.000Z",
  "extractedInfo": null,
  "pageContent": "محتوى الصفحة..."
}
```

## أنواع الأخطاء

| نوع الخطأ | الوصف | الحل |
|-----------|-------|------|
| `voucher_error` | خطأ في رقم البطاقة | تحقق من صحة رقم البطاقة |
| `captcha_error` | خطأ في حل الكابتشا | سيتم إعادة المحاولة تلقائياً |
| `stage1_validation_error` | خطأ في رقم الهاتف | تحقق من صحة رقم الهاتف |
| `api_system_error` | خطأ في النظام | تحقق من الاتصال والإعدادات |

## الإعدادات

### متطلبات النظام
- Node.js 16+
- مفتاح AZCaptcha API صحيح
- اتصال إنترنت مستقر

### متغيرات البيئة
```env
AZCAPTCHA_API_KEY=your_actual_api_key_here
PORT=3000
```

## المراقبة والتسجيل

### عرض العمليات
```bash
# الحصول على آخر العمليات
curl http://localhost:3000/api/operations?limit=10

# الحصول على الإحصائيات
curl http://localhost:3000/api/stats/overall
```

### لوحة المراقبة
افتح http://localhost:3000/dashboard لمراقبة العمليات في الوقت الفعلي.

## الأمان

### أفضل الممارسات
- لا تشارك مفاتيح API
- استخدم HTTPS في الإنتاج
- راقب معدل الطلبات
- احتفظ بنسخ احتياطية من قاعدة البيانات

### حدود الاستخدام
- تجنب الإفراط في الطلبات
- احترم شروط استخدام الموقع
- استخدم النظام بمسؤولية

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الاتصال
```
Error: connect ECONNREFUSED
```
**الحل**: تأكد من تشغيل الخادم: `npm start`

#### 2. خطأ في مفتاح AZCaptcha
```
Error: Invalid API key
```
**الحل**: تحقق من مفتاح API في ملف `.env`

#### 3. مهلة انتهاء الوقت
```
Error: timeout of 120000ms exceeded
```
**الحل**: تحقق من اتصال الإنترنت وحالة الموقع

## التطوير

### إضافة ميزات جديدة
1. عدّل ملف `api-client.js`
2. أضف اختبارات في `test-api-client.js`
3. حدّث الوثائق

### المساهمة
- اتبع معايير الكود الموجودة
- أضف اختبارات للميزات الجديدة
- حدّث الوثائق

## الدعم

### الحصول على المساعدة
- راجع ملفات التوثيق
- تحقق من ملفات الاختبار
- راجع لوحة المراقبة للأخطاء

### الإبلاغ عن المشاكل
- وصف المشكلة بالتفصيل
- أرفق رسائل الخطأ
- اذكر نوع الخدمة المستخدمة

---

## خلاصة

الطريقة الجديدة (API Client) توفر:
- **أداء أفضل** بشكل كبير
- **استقرار أعلى** في التشغيل
- **استهلاك أقل** للموارد
- **سهولة أكبر** في الصيانة

**التوصية**: استخدم API Client كطريقة افتراضية للحصول على أفضل تجربة.
