<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التعبئة الآلي - الجزائر تيليكوم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            direction: ltr;
            text-align: center;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group select {
            cursor: pointer;
            text-align-last: center;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 نظام التعبئة الآلي</h1>
            <p>الجزائر تيليكوم - تعبئة بطاقات الإنترنت</p>
        </div>
        
        <form id="rechargeForm">
            <div class="form-group">
                <label for="serviceType">🌐 نوع الخدمة</label>
                <select id="serviceType" name="serviceType" required>
                    <option value="4g">4G LTE</option>
                    <option value="adsl">ADSL</option>
                </select>
            </div>

            <div class="form-group">
                <label for="method">⚡ طريقة التنفيذ</label>
                <select id="method" name="method" required>
                    <option value="api">API Client (سريع ومستقر) - مُوصى به</option>
                    <option value="browser">Puppeteer (متصفح آلي)</option>
                </select>
                <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                    💡 API Client أسرع وأكثر استقراراً ويستهلك موارد أقل
                </small>
            </div>

            <div class="form-group">
                <label for="phoneNumber">📱 رقم الهاتف</label>
                <input type="text" id="phoneNumber" name="phoneNumber" placeholder="0123456789" required>
            </div>

            <div class="form-group">
                <label for="voucherCode">💳 رقم البطاقة</label>
                <input type="text" id="voucherCode" name="voucherCode" placeholder="1234567890123456" required>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">
                ⚡ بدء التعبئة الآلية
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري تنفيذ عملية التعبئة... يرجى الانتظار</p>
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="note">
            <strong>ملاحظة:</strong> هذا النظام يدعم تعبئة بطاقات 4G و ADSL ويستخدم خدمة AZCaptcha لحل الكابتشا تلقائياً. تأكد من إعداد مفتاح API في ملف .env
            <br><br>
            <strong>الخدمات المدعومة:</strong>
            <br>• 4G LTE: تعبئة بطاقات الإنترنت للهواتف المحمولة
            <br>• ADSL: تعبئة بطاقات الإنترنت للخطوط الثابتة
            <br><br>
            <button type="button" class="btn" id="checkApiBtn" style="margin-top: 10px; font-size: 14px; padding: 8px;">
                🔑 التحقق من مفتاح AZCaptcha
            </button>
            <br>
            <a href="/dashboard" target="_blank" class="btn" style="margin-top: 10px; font-size: 14px; padding: 8px; text-decoration: none; display: inline-block;">
                📊 لوحة مراقبة العمليات
            </a>
        </div>
    </div>

    <script>
        document.getElementById('rechargeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const serviceType = document.getElementById('serviceType').value;
            const phoneNumber = document.getElementById('phoneNumber').value;
            const voucherCode = document.getElementById('voucherCode').value;
            const method = document.getElementById('method').value;
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');

            // تحديد endpoint حسب الطريقة المختارة
            const endpoint = method === 'api' ? '/recharge-api' : '/recharge';
            const methodName = method === 'api' ? 'API Client' : 'Puppeteer';

            // Reset UI
            result.style.display = 'none';
            loading.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.textContent = `جاري التنفيذ (${serviceType.toUpperCase()}) - ${methodName}...`;

            // تحديث رسالة التحميل
            const loadingText = loading.querySelector('p');
            loadingText.textContent = `جاري تنفيذ عملية التعبئة باستخدام ${methodName}... يرجى الانتظار`;

            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        serviceType: serviceType,
                        phoneNumber: phoneNumber,
                        voucherCode: voucherCode
                    })
                });
                
                const data = await response.json();
                
                // Hide loading
                loading.style.display = 'none';
                
                // Show result
                result.style.display = 'block';
                result.className = 'result ' + (data.success ? 'success' : 'error');

                // إنشاء محتوى النتيجة
                let resultHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>${data.success ? '✅' : '❌'} ${data.message}</strong>
                    </div>
                `;

                // إضافة معلومات الطريقة المستخدمة
                if (data.method) {
                    resultHTML += `
                        <div style="margin-bottom: 10px; font-size: 14px; color: #666;">
                            🔧 الطريقة المستخدمة: ${data.method}
                        </div>
                    `;
                }

                // إضافة المعلومات المستخرجة إذا كانت متوفرة
                if (data.extractedInfo && Object.keys(data.extractedInfo).length > 0) {
                    resultHTML += `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <strong>📋 المعلومات المستخرجة:</strong><br>
                    `;

                    if (data.extractedInfo.operationNumber) {
                        resultHTML += `🆔 رقم العملية: ${data.extractedInfo.operationNumber}<br>`;
                    }
                    if (data.extractedInfo.phoneNumber) {
                        resultHTML += `📞 رقم الهاتف المؤكد: ${data.extractedInfo.phoneNumber}<br>`;
                    }
                    if (data.extractedInfo.clientNumber) {
                        resultHTML += `👤 رقم العميل: ${data.extractedInfo.clientNumber}<br>`;
                    }
                    if (data.extractedInfo.date) {
                        resultHTML += `📅 التاريخ: ${data.extractedInfo.date}<br>`;
                    }
                    if (data.extractedInfo.time) {
                        resultHTML += `🕐 الوقت: ${data.extractedInfo.time}<br>`;
                    }

                    resultHTML += `</div>`;
                }

                // إضافة معلومات إضافية
                resultHTML += `
                    <div style="margin-top: 15px; font-size: 12px; color: #888;">
                        ⏰ وقت العملية: ${new Date(data.timestamp).toLocaleString('ar-DZ')}
                    </div>
                `;

                result.innerHTML = resultHTML;
                
            } catch (error) {
                loading.style.display = 'none';
                result.style.display = 'block';
                result.textContent = 'حدث خطأ في الاتصال: ' + error.message;
                result.className = 'result error';
            }
            
            // Reset button
            submitBtn.disabled = false;
            submitBtn.textContent = '⚡ بدء التعبئة الآلية';
        });

        // Check AZCaptcha API key
        document.getElementById('checkApiBtn').addEventListener('click', async function() {
            const checkBtn = this;
            const originalText = checkBtn.textContent;

            checkBtn.disabled = true;
            checkBtn.textContent = '🔄 جاري التحقق...';

            try {
                const response = await fetch('/check-captcha-api');
                const data = await response.json();

                const result = document.getElementById('result');
                result.style.display = 'block';
                result.textContent = data.message;
                result.className = 'result ' + (data.success ? 'success' : 'error');

            } catch (error) {
                const result = document.getElementById('result');
                result.style.display = 'block';
                result.textContent = 'خطأ في التحقق من مفتاح API: ' + error.message;
                result.className = 'result error';
            }

            checkBtn.disabled = false;
            checkBtn.textContent = originalText;
        });

        // تحديث تسميات الحقول حسب نوع الخدمة
        document.getElementById('serviceType').addEventListener('change', function() {
            const serviceType = this.value;
            const phoneLabel = document.querySelector('label[for="phoneNumber"]');
            const loadingText = document.querySelector('.loading p');

            if (serviceType === '4g') {
                phoneLabel.textContent = '📱 رقم الهاتف (4G LTE)';
                loadingText.textContent = 'جاري تنفيذ عملية التعبئة 4G... يرجى الانتظار';
            } else if (serviceType === 'adsl') {
                phoneLabel.textContent = '📱 رقم الهاتف (ADSL)';
                loadingText.textContent = 'جاري تنفيذ عملية التعبئة ADSL... يرجى الانتظار';
            }
        });
    </script>
</body>
</html>
