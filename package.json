{"name": "algeria-telecom-recharge-bot", "version": "1.0.0", "description": "Automated recharge system for Algeria Telecom 4G vouchers", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "test": "node test-api.js", "test-api-client": "node test-api-client.js", "test-db": "node test-database.js", "example": "node example-usage.js", "example-api-client": "node example-api-client.js", "quick-test": "node quick-test.js"}, "dependencies": {"axios": "^1.11.0", "cheerio": "^1.1.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "form-data": "^4.0.4", "moment": "^2.30.1", "puppeteer": "^21.11.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["algeria-telecom", "automation", "recharge", "4g"], "author": "Your Name", "license": "MIT"}